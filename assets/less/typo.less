@import 'vw_values.less';
@import 'constants.less';

.hugeTitle, .bigTitle, .biggerTitle {
  &.white {
    color: @almostWhite;
  }
}


.hugeTitle {
  font-size: @vw140;
  text-decoration: none;
  font-family: @headingFont;
  font-weight: 400; /* Bebas Neue only has one weight */
  font-style: normal;
  letter-spacing: 2px;
  line-height: .9;
  .transitionMore(opacity, .3s);
  &.link {
    cursor: pointer;
    color: @primaryColor;
    &:hover {
      opacity: .6;
    }
    span {
      cursor: pointer;
    }
  }
}

.bigTitle {
  font-size: @vw100 + @vw40;
  line-height: .9;
  letter-spacing: 2px;
  font-family: @headingFont;
  font-weight: 400;
  font-style: normal;
  &.compact {
    font-size: @vw100 + @vw30;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-family: @headingFont;
    font-weight: 400;
    font-style: normal;
  }
  .line {
    position: relative;
    overflow: hidden;
  }
}

.mediumTitle {
  font-size: @vw50;
  letter-spacing: 2px;
  font-family: @headingFont;
  font-weight: 400;
  font-style: normal;
  line-height: 1.1;
  text-transform: uppercase;
}

.subTitle {
  font-size: @vw24;
  line-height: 1.4;
  font-family: @headingFont;
  font-weight: 400;
  font-style: normal;
  letter-spacing: 1px;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.tinyTitle {
  font-size: @vw16;
  line-height: 1.4;
  text-transform: uppercase;
  font-family: @headingFont;
  font-weight: 400;
  letter-spacing: 1px;
}

.normalTitle {
  font-size: @vw50;
  line-height: 1.1;
  text-transform: uppercase;
  font-family: @headingFont;
  font-weight: 400;
  letter-spacing: 1px;
  &.primary {
    color: @primaryColor;
  }
  .primary {
    color: @primaryColor;
  }
  .line {
    position: relative;
    overflow: hidden;
  }
}

.link, .smallTitle {
  font-size: @vw32;
  line-height: 1.4;
  text-transform: uppercase;
  font-family: @headingFont;
  font-weight: 400;
  letter-spacing: 1px;
  &.primary {
    color: @primaryColor;
  }
}

.smallTitle {
  line-height: 1.1;
}

.text {
  font-family: @bodyFont;
  line-height: 1.8; 
  &.bigger {
    font-size: @vw22;
    text-transform: uppercase;
    p {
      font-size: @vw22;
      text-transform: uppercase;
    }
  }
  &.white {
    p {
      color: @grey;
    }
  }
  &:not(:first-child) {
    margin-top: @vw20;
  }
  p {
    font-family: @bodyFont;
    line-height: 1.8;
    font-weight: 400;
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}

@media all and (max-width: 1160px) {
  .hugeTitle {
    font-size: @vw72-1160;
  }

  .bigTitle {
    font-size: @vw82-1160;
    &.compact {
      font-size: @vw70-1160;
    }
  }

  .mediumTitle {
    font-size: @vw50-1160;
  }

  .normalTitle {
    font-size: @vw40-1160;
  }

  .subTitle {
    font-size: @vw24-1160;
  }

  .tinyTitle {
    font-size: @vw16-1160;
  }

  .text {
    &.bigger {
      font-size: @vw22-1160;
      p {
        font-size: @vw22-1160;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-1160;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: @vw100-580;
  }

  .bigTitle {
    font-size: @vw70-580;
    &.compact {
      font-size: @vw70-580;
    }
  }

  .mediumTitle {
    font-size: @vw50-580;
  }

  .normalTitle {
    font-size: @vw40-580;
  }

  .subTitle {
    font-size: @vw24-580;
  }

  .tinyTitle {
    font-size: @vw16-580;
  }

  .text {
    &.bigger {
      font-size: @vw22-580;
      p {
        font-size: @vw22-580;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-580;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }
}

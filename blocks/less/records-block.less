// out: ../css/records-block.css, compress: true, strictMath: true
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.recordsBlock {
  padding: @vw50 0;

  .recordsBlockHeader {
    margin-bottom: @vw30;

    .biggerTitle {
      margin-bottom: @vw20;
    }
  }

  .viewAllLink {
    margin-top: @vw30;
    text-align: center;

    .button {
      display: inline-block;
      padding: @vw12 @vw24;
      background: @secondaryColor;
      color: @hardWhite;
      text-decoration: none;
      border-radius: @vw5;
      font-weight: bold;
      transition: background 0.3s ease;

      &:hover {
        background: darken(@secondaryColor, 10%);
      }

      i {
        font-size: @vw16;
        display: inline-block;
        vertical-align: middle;
        margin-left: @vw10;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .recordsBlock {
    padding: @vw50-1160 0;

    .recordsBlockHeader {
      margin-bottom: @vw30-1160;

      .biggerTitle {
        margin-bottom: @vw20-1160;
      }
    }

    .viewAllLink {
      margin-top: @vw30-1160;

      .button {
        padding: @vw12-1160 @vw24-1160;
        border-radius: @vw5-1160;

        i {
          font-size: @vw16-1160;
          margin-left: @vw10-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .recordsBlock {
    padding: @vw40-580 0;

    .recordsBlockHeader {
      margin-bottom: @vw20-580;

      .biggerTitle {
        margin-bottom: @vw15-580;
      }
    }

    .recordsGrid {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    .viewAllLink {
      margin-top: @vw25-580;

      .button {
        padding: @vw10-580 @vw20-580;
        border-radius: @vw5-580;
        font-size: @vw14-580;

        i {
          font-size: @vw14-580;
          margin-left: @vw8-580;
        }
      }
    }
  }
}

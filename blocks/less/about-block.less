// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.aboutBlock {
  padding: @vw100 0;
  &.inview {
    .vinylCol {
      .vinyl {
        opacity: 1;
        transform: translateY(-50%) rotate(0deg);
        transition: transform 1.5s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.5s ease;
        animation: rotate 10s linear infinite;
      }
    }
  }

  .cols {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -@vw20;
  }

  .hugeTitle, .text {
    padding-right: @vw130;
  }

  .col {
    width: 50%;
    padding: 0 @vw20;
    position: relative;
  }

  // Vinyl Column
  .vinylCol {
    position: relative;
    height: auto;
    .transform(translateX(-30%) scale(1.4));
    .vinylWrapper {
      position: relative;
      width: 100%;
      transform-origin: center;
      -webkit-transform-origin: center;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .vinyl {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 0;
      .transform(translateX(-50%) translateY(-50%) rotate(60deg));
      opacity: 0;
      transition: transform 0.5s ease, opacity 0.5s ease;
      transform-origin: center;
      cursor: grab;
      .paddingRatio(1,1);
      &:active {
        cursor: grabbing;
      }
      img {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        object-fit: contain;
        border-radius: 50%;
      }

      .defaultVinyl {
        width: 100%;
        height: 100%;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  // Text Column
  .textCol {
    .innerContent {
      padding: @vw20 0;
    }
    .text {
      margin-bottom: @vw30;
      font-size: @vw18;
      line-height: 1.6;

      p {
        margin-bottom: @vw15;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    .additionalImageWrapper {
      padding-right: @vw100;
      text-align: right;
    }
    .additionalImage {
      display: inline-block;
      margin-top: @vw60;
      width: @vw100 * 2;
      img {
        max-width: 100%;
        height: auto;
        display: block;
      }
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .aboutBlock {
    padding: @vw100-1160 0;

    .cols {
      margin: 0 -@vw20-1160;
    }

    .hugeTitle, .text {
      padding-right: @vw130-1160;
    }

    .col {
      padding: 0 @vw20-1160;
    }

    .vinylCol {
      .transform(translateX(-30%) scale(1.3));

      .vinyl {
        width: @vw300-1160 * 2;
        height: @vw300-1160 * 2;
      }
    }

    .textCol {
      .innerContent {
        padding: @vw20-1160 0;
      }

      .text {
        margin-bottom: @vw30-1160;
        font-size: @vw18-1160;

        p {
          margin-bottom: @vw15-1160;
        }
      }
      .additionalImageWrapper {
        padding-right: @vw100-1160;
      }
      .additionalImage {
        margin-top: @vw30-1160;
        width: @vw100-1160 * 2;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .aboutBlock {
    padding: @vw100-580 0;

    .cols {
      flex-direction: column;
      margin: 0 -@vw20-580;
    }

    .hugeTitle, .text {
      padding-right: @vw50-580;
    }

    .col {
      width: 100%;
      padding: 0 @vw20-580;

      &.vinylCol {
        margin-bottom: @vw50-580;
        height: @vw300-580;
      }
    }

    .vinylCol {
      .transform(translateX(-20%) scale(1.2));

      .vinyl {
        width: @vw100-580 * 2.5;
        height: @vw100-580 * 2.5;
      }
    }

    .textCol {
      .innerContent {
        padding: @vw20-580 0;
      }

      .text {
        margin-bottom: @vw30-580;
        font-size: @vw18-580;

        p {
          margin-bottom: @vw15-580;
        }
      }
      .additionalImageWrapper {
        padding-right: 0;
        text-align: left;
      }
      .additionalImage {
        margin-top: @vw30-580;
        width: @vw100-580 * 2;
      }
    }
  }
}

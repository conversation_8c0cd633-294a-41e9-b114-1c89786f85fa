<?php
/**
 * Block Name: Latest Records
 * Description: Display the most recent records from your collection
 */

// Get block values
$title = get_field('title');
$records_count = get_field('records_count') ?: 8;
$show_artist = get_field('show_artist');
$show_year = get_field('show_year');
$show_format = get_field('show_format');
$link = get_field('link');

// Get latest records based on release date
$latest_records = new WP_Query(array(
    'post_type' => 'record',
    'posts_per_page' => $records_count,
    'meta_key' => '_record_release_date',
    'orderby' => 'meta_value',
    'order' => 'DESC',
    'meta_query' => array(
        array(
            'key' => '_record_release_date',
            'compare' => 'EXISTS',
        ),
    ),
    'tax_query' => array(
        'relation' => 'AND',
        // Only show albums
        array(
            'taxonomy' => 'record_product_type',
            'field' => 'slug',
            'terms' => 'album',
        ),
        // Exclude records with format 'Spellen' or other non-music formats
        array(
            'taxonomy' => 'record_format',
            'field' => 'slug',
            'terms' => array('spellen', 'bordspellen', 'games', 'board-games'),
            'operator' => 'NOT IN',
        ),
    ),
));

// Block ID
$block_id = 'latestRecordsSlider-' . $block['id'];

// Block classes
$classes = 'latestRecordsSlider';
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}
?>

<section id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" data-init>
    <div class="contentWrapper">
        <div class="col">
            <?php if ($title) : ?>
                <h2 class="normalTitle" data-lines data-words><?php echo esc_html($title); ?></h2>
            <?php endif; ?>
        </div>
        <div class="col right">
            <a href="<?php echo esc_url($link['url']); ?>" title="<?php echo esc_html($link['title']);  ?>" class="textLink" <?php echo $link['target'] ? 'target="_blank" rel="noopener noreferrer"' : ''; ?>>
                <i class="icon-arrow-right"></i>
                <span class="innerMask">
                    <span class="innerWrapper">
                        <span class="innerText absolute"><?php echo esc_html($link['title']); ?></span>
                        <span class="innerText absolute" aria-hidden="true"><?php echo esc_html($link['title']); ?></span>
                    </span>
                </span>
                <span class="divider">
                    <svg xmlns="http://www.w3.org/2000/svg" width="184.5" height="2" viewBox="0 0 184.5 2">
                        <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                    </svg>
                </span>
            </a>
        </div>
        <div class="sliderWrapper">
            <?php if ($latest_records->have_posts()) : ?>
                <div class="recordsSlider">
                    <?php while ($latest_records->have_posts()) : $latest_records->the_post(); ?>
                        <div class="recordSlide">
                            <a href="<?php the_permalink(); ?>" class="recordLink">
                                <div class="recordCover">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <img class="lazy" data-src="<?php the_post_thumbnail_url('record-cover'); ?>" alt="<?php the_title(); ?>">
                                    <?php else : ?>
                                        <div class="defaultCover">
                                            <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="256" cy="256" r="240" fill="#333" />
                                                <circle cx="256" cy="256" r="120" fill="#F5D042" />
                                                <circle cx="256" cy="256" r="30" fill="#333" />
                                            </svg>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="recordInfo">
                                    <h3 class="smallTitle"><?php the_title(); ?></h3>

                                    <?php if ($show_artist) : ?>
                                        <?php
                                        $artists = get_the_terms(get_the_ID(), 'record_artist');
                                        if ($artists && !is_wp_error($artists)) :
                                        ?>
                                            <div class="recordArtist">
                                                <?php echo esc_html($artists[0]->name); ?>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php if ($show_year) : ?>
                                        <?php
                                        // Try to get release date first
                                        $release_date = get_post_meta(get_the_ID(), '_record_release_date', true);
                                        $year = '';

                                        if (!empty($release_date)) {
                                            // Extract year from release date
                                            $date_parts = explode('-', $release_date);
                                            if (count($date_parts) > 0) {
                                                $year = $date_parts[0];
                                            }
                                        } else {
                                            // Fallback to year field
                                            $year = get_post_meta(get_the_ID(), '_record_year', true);
                                        }

                                        if ($year):
                                        ?>
                                            <div class="recordYear"><?php echo esc_html($year); ?></div>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php if ($show_format) : ?>
                                        <?php
                                        $formats = get_the_terms(get_the_ID(), 'record_format');
                                        if ($formats && !is_wp_error($formats)) :
                                            $format_names = array();
                                            foreach ($formats as $format) {
                                                $format_names[] = $format->name;
                                            }
                                        ?>
                                            <div class="recordFormat"><?php echo esc_html(implode(', ', $format_names)); ?></div>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php
                                    $suggested_price = get_post_meta(get_the_ID(), '_record_suggested_price', true);
                                    $price = get_post_meta(get_the_ID(), '_record_price', true);
                                    if ($price && rewindrecords_show_prices()):
                                    ?>
                                        <div class="smallTitle primary">€<?php echo number_format($price, 2, ',', '.'); ?></div>
                                    <?php endif; ?>
                                </div>
                            </a>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else : ?>
                <div class="noRecords">
                    <p><?php _e('No records found.', 'rewindrecords'); ?></p>
                </div>
            <?php endif; ?>
            <?php wp_reset_postdata(); ?>
        </div>
    </div>
</section>

<?php wp_footer(); ?>
</div>
<?php $year = (new DateTime)->format("Y"); ?>
</div>
</div>
<div class="noise" style="background:url('<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-noise'))); ?>" alt="<?php echo get_theme_mod('customTheme-main-callout-title') ?>');"></div>
<footer class="footer">
  <div class="contentWrapper">
    <div class="innerWrapper">
      <svg class="topBorder" width="1592" height="1" viewBox="0 0 1592 1">
        <line data-name="Line 3" x2="1592" transform="translate(0 0.5)" fill="none" stroke="#eee" stroke-width="1" stroke-dasharray="5"/>
      </svg>
  </div>
    <div class="cols">
      <div class="col">
        <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title') ?>" class="logo">
          <svg viewBox="0 0 195.01 60.15">
            <path class="cls-1" d="M83.69,7.53h15.04v6.94h-6.02v6.58h5.64v6.6h-5.64v7.63h6.62v6.94h-15.64V7.53Z"/>
            <rect class="cls-1" x="137.68" y="7.53" width="9.02" height="34.7"/>
            <path class="cls-1" d="M170.47,7.53v34.7h-7.91l-4.69-15.77v15.77h-7.54V7.53h7.54l5.06,15.62V7.53h7.54Z"/>
            <path class="cls-1" d="M60.87,55.73c.27-.22.62-.34.97-.33.23,0,.47.02.7.06.24.04.48.06.72.06.07,0,.16,0,.29-.01.13,0,.2-.01.22-.01l.1-.1c0-.07,0-.18.01-.33s.02-.32.03-.49.02-.35.04-.51.03-.27.03-.34c0-.09.02-.17.04-.25.01-.09.03-.17.06-.25l-.05-2.48v-.48l-.15-2.99-.2-.2c-.09,0-.23,0-.43-.01l-.66-.03c-.24,0-.46-.02-.66-.04-.2-.02-.35-.03-.43-.03-.32-.03-.62-.16-.85-.39-.23-.22-.35-.53-.34-.85,0-.32.1-.63.3-.87.21-.25.51-.38.84-.37h.08c.05,0,.08,0,.08.03.33.03.67.04,1,.01.4-.03.8-.03,1.2-.01.38.01.77.07,1.14.16.32.07.61.26.81.52.05.06.08.14.1.22.03.11.07.22.11.34.04.11.09.21.15.3.04.07.11.12.19.13h.05c.53-.43,1.09-.82,1.68-1.16.58-.35,1.25-.53,1.94-.53.42,0,.83.07,1.23.22.38.13.72.33,1.03.59.29.26.53.57.7.92.18.37.27.78.27,1.2.02.56-.14,1.12-.44,1.59-.35.47-.92.72-1.51.66-.3.01-.6-.1-.81-.32-.21-.22-.37-.49-.48-.77-.12-.31-.2-.62-.24-.95-.04-.33-.07-.61-.09-.85,0-.08-.08-.13-.15-.13h-.18c-.08,0-.16,0-.24.01-.08.01-.16.05-.22.11-.12.09-.23.19-.33.3l-.48.48-.48.48-.3.3c-.1.11-.2.22-.29.34-.13.17-.25.34-.37.52-.11.17-.21.36-.3.54-.07.12-.11.25-.13.39v2.86c.03.08.05.17.06.25.02.08.03.17.04.25,0,.05,0,.15.03.3s.03.31.04.48c0,.17.02.33.03.48,0,.15.01.26.01.33.04.15.15.28.3.33.16.04.32.05.48.05.19,0,.38-.02.57-.05.19-.03.38-.05.57-.05.38,0,.75.11,1.06.32.31.2.5.55.48.92.02.28-.06.57-.23.8-.15.2-.36.34-.59.43-.26.1-.53.16-.8.18-.29.03-.56.04-.81.04-.28,0-.57-.02-.85-.05-.28-.03-.56-.05-.85-.05-.13,0-.25,0-.34.01-.13.02-.26.05-.39.09h-2.88c-.4,0-.79-.14-1.1-.41-.32-.25-.5-.63-.49-1.04-.02-.35.13-.68.39-.91"/>
            <path class="cls-1" d="M73.88,50.03c.09-.61.25-1.21.47-1.78.22-.56.51-1.09.87-1.57.37-.49.84-.89,1.38-1.18.09-.06.18-.11.28-.15l.43-.22c.15-.08.29-.14.42-.2s.22-.1.27-.11c.18-.08.37-.12.57-.11.19,0,.38.01.57.01.97-.03,1.92.24,2.73.77.79.56,1.48,1.23,2.08,1.99.11.18.19.37.27.57.11.28.21.58.3.91.09.33.17.65.24.96.06.23.09.46.1.7,0,.03,0,.11-.01.22s-.01.18-.01.22c-.13.24-.35.42-.61.51-.26.08-.52.15-.78.2-.19,0-.52,0-1-.01-.48,0-.99-.01-1.53-.01s-1.02,0-1.51.01c-.48,0-.81.01-1,.01s-.41,0-.67.01l-.77.03c-.25,0-.56.03-.94.06l-.1.08s0,.08-.01.18-.01.17-.01.2c0,.48.09.95.27,1.4.17.43.42.82.73,1.16.31.33.69.6,1.1.79.44.2.91.3,1.39.29.05,0,.16,0,.34-.01s.29-.01.34-.01c.24-.19.52-.32.81-.39.29-.07.56-.2.78-.39.18-.16.34-.35.47-.56.14-.22.29-.43.46-.63.15-.19.32-.36.52-.51.2-.14.44-.21.68-.2.23,0,.45.07.62.23.16.15.25.36.24.58,0,.37-.08.73-.25,1.05-.17.31-.35.62-.56.92-.49.72-1.19,1.27-2,1.58-.82.31-1.68.48-2.55.47-.8.01-1.6-.16-2.33-.51-.67-.32-1.27-.79-1.75-1.37-.48-.59-.85-1.26-1.09-1.99-.25-.76-.38-1.56-.38-2.37,0-.61.05-1.22.14-1.82M76.11,49.68c0,.05.03.08.1.08h5.84c.05,0,.1-.03.14-.06.05-.03.1-.07.14-.11.02-.06.03-.12.03-.18v-.18c.01-.37-.09-.72-.3-1.03-.2-.27-.46-.5-.76-.66-.32-.16-.65-.28-1-.35-.34-.07-.68-.11-1.03-.11-.37,0-.73.05-1.09.14-.36.09-.7.24-1.01.44-.3.2-.56.47-.76.77-.21.33-.31.71-.3,1.1v.16Z"/>
            <path class="cls-1" d="M85.69,48.96c.21-.84.56-1.63,1.04-2.35.45-.69,1.05-1.28,1.75-1.72.74-.46,1.6-.69,2.47-.67h.33c.09,0,.17,0,.25.03.13,0,.26.03.39.06.19.04.4.09.61.14s.41.11.59.16.31.1.38.11c.27.24.55.48.85.73.29.25.56.53.81.82.24.29.44.62.59.96.15.35.23.72.23,1.1,0,.26-.04.52-.11.77-.07.25-.18.49-.33.7-.14.2-.32.37-.53.49-.23.13-.48.2-.75.19-.5.01-.98-.19-1.31-.56-.35-.35-.54-.82-.53-1.32,0-.2.03-.4.1-.59.07-.2.1-.41.1-.62v-.4l-.2-.2c-.07-.09-.18-.16-.3-.18h-1.67c-.09.01-.18.03-.27.06-.14.04-.26.08-.34.11-.42.18-.8.43-1.13.75-.29.29-.53.63-.7,1-.17.39-.29.79-.35,1.21-.07.45-.1.91-.1,1.37,0,.47.05.94.13,1.4.07.46.23.91.48,1.3.05.05.14.14.25.27.12.13.24.26.38.39.13.13.26.26.39.38.13.12.22.2.27.25.08.07.16.14.25.19.07.05.15.09.23.11h2.18c.25,0,.5-.07.71-.22.21-.15.39-.33.54-.53.16-.22.31-.45.43-.7.12-.24.25-.47.39-.7.12-.19.27-.37.43-.53.14-.14.33-.22.53-.22.27-.02.52.11.66.34.14.23.2.5.2.77,0,.59-.15,1.16-.44,1.67-.3.52-.68.99-1.14,1.38-.46.4-.99.72-1.56.95-.54.23-1.11.35-1.69.35-.8,0-1.6-.2-2.3-.58-.7-.38-1.33-.89-1.85-1.51-.53-.62-.94-1.33-1.23-2.09-.29-.74-.44-1.53-.44-2.33,0-.86.12-1.71.34-2.54"/>
            <path class="cls-1" d="M96.88,48.92c.21-.85.57-1.66,1.07-2.38.49-.69,1.14-1.26,1.89-1.66.85-.43,1.79-.65,2.74-.62.73-.02,1.45.14,2.1.47.63.34,1.23.75,1.77,1.23.02.02.04.04.05.06.02.03.03.04.05.04.2.39.41.77.62,1.15.21.38.41.77.58,1.16.18.39.32.8.43,1.21.11.42.16.85.16,1.28,0,.05,0,.15-.01.29s-.01.23-.01.27c-.03.1-.08.28-.14.54s-.13.54-.2.84-.15.58-.21.85-.11.46-.13.56c-.16.51-.4,1-.7,1.44-.33.5-.71.97-1.15,1.38-.43.41-.91.75-1.43,1.03-.47.26-1,.39-1.53.4-.2,0-.39-.01-.58-.05-.12-.03-.23-.07-.34-.13-.09-.05-.19-.11-.29-.16-.14-.07-.28-.13-.43-.16-.08-.02-.24-.04-.48-.08-.17-.02-.34-.05-.51-.1-.12-.05-.23-.1-.34-.16l-.43-.23c-.14-.08-.28-.14-.4-.2-.08-.03-.15-.07-.21-.11-.38-.26-.69-.61-.92-1.01-.28-.47-.53-.96-.72-1.47-.2-.51-.35-1.03-.47-1.57-.1-.44-.15-.89-.16-1.34,0-.93.11-1.86.34-2.76M98.75,52.26c.03.24.06.47.11.71.05.23.11.45.19.67.05.17.14.33.27.47l1.19,1.19c.15.04.29.07.44.1l.34.05c.1.02.2.02.3.03.12.03.24.04.35.03h.53c.34,0,.63,0,.87-.01.28-.02.56-.08.82-.19.65-.49,1.15-1.15,1.44-1.91.25-.76.37-1.56.35-2.36,0-.49-.05-.98-.16-1.46-.12-.48-.27-.94-.44-1.4-.08-.22-.23-.41-.42-.54-.2-.15-.41-.27-.63-.38-.23-.11-.47-.21-.71-.29-.22-.07-.42-.16-.62-.28h-1.59c-.11.05-.22.12-.33.19-.2.13-.35.22-.45.29-.37.26-.7.57-.97.94-.24.33-.44.69-.58,1.08-.14.39-.24.79-.28,1.2-.04.42-.06.87-.06,1.34,0,.19.02.37.04.56"/>
            <path class="cls-1" d="M109.45,55.73c.27-.22.62-.34.97-.33.23,0,.47.02.7.06.24.04.48.06.72.06.07,0,.16,0,.29-.01.13,0,.2-.01.21-.01l.1-.1c0-.07,0-.18.01-.33s.02-.32.03-.49.02-.35.04-.51.03-.27.03-.34c0-.09.02-.17.04-.25.01-.09.03-.17.06-.25l-.05-2.48v-.48l-.15-2.99-.2-.2c-.08,0-.23,0-.43-.01l-.66-.03c-.24,0-.46-.02-.66-.04-.2-.02-.35-.03-.43-.03-.32-.03-.62-.16-.85-.39-.23-.22-.35-.53-.34-.85,0-.32.1-.63.3-.87.21-.25.51-.38.83-.37h.08c.05,0,.08,0,.08.03.33.03.67.04,1,.01.4-.03.8-.03,1.2-.01.38.01.77.07,1.14.16.32.07.61.26.81.52.05.06.08.14.1.22.03.11.07.22.11.34.04.11.09.21.15.3.04.07.11.12.19.13h.05c.53-.43,1.09-.82,1.68-1.16.58-.35,1.25-.53,1.94-.53.42,0,.83.07,1.23.22.38.13.72.33,1.03.59.29.26.53.57.7.92.18.37.27.78.27,1.2.02.56-.14,1.12-.44,1.59-.35.47-.92.72-1.51.66-.3.01-.6-.1-.81-.32-.21-.22-.37-.49-.48-.77-.12-.31-.2-.62-.24-.95-.04-.33-.07-.61-.09-.85,0-.08-.08-.13-.15-.13h-.18c-.08,0-.16,0-.24.01-.08.01-.16.05-.21.11-.12.09-.23.19-.33.3l-.48.48-.48.48-.3.3c-.1.11-.2.22-.29.34-.13.17-.25.34-.37.52-.11.17-.21.36-.3.54-.07.12-.11.25-.13.39v2.86c.03.08.05.17.06.25.02.08.03.17.04.25,0,.05,0,.15.03.3s.03.31.04.48c0,.17.02.33.03.48,0,.15.01.26.01.33.04.15.15.28.3.33.16.04.32.05.48.05.19,0,.38-.02.57-.05.19-.03.38-.05.57-.05.38,0,.75.11,1.06.32.31.2.5.55.48.92.02.28-.06.57-.23.8-.15.2-.36.34-.59.43-.26.1-.53.16-.8.18-.29.03-.56.04-.81.04-.28,0-.57-.02-.85-.05-.28-.03-.56-.05-.85-.05-.13,0-.25,0-.34.01-.13.02-.26.05-.39.09h-2.89c-.4,0-.79-.14-1.1-.41-.32-.25-.5-.63-.49-1.04-.02-.35.13-.68.39-.91"/>
            <path class="cls-1" d="M128.41,44.52c.37.07.72.2,1.04.4.15.11.3.23.43.37.14.12.32.18.51.16.05,0,.1-.01.15-.03l.2-.61v-2.35c0-.22-.16-.35-.47-.38s-.65-.07-1.03-.11c-.36-.04-.71-.15-1.03-.32-.33-.21-.52-.6-.47-.99,0-.21.07-.42.23-.57.16-.15.37-.22.58-.22h3.06c.14,0,.28.05.41.13.14.09.26.19.37.32.11.13.2.27.27.42.06.13.1.28.1.43v7.82l.2.48v5.74l.25.2c.17,0,.41-.01.72-.04.31-.03.62-.02.92.03.28.03.55.12.8.27.23.14.36.39.34.66.01.29-.07.58-.24.81-.16.2-.38.36-.62.46-.26.1-.54.17-.82.19-.3.03-.57.04-.82.04-.5,0-1-.05-1.48-.18-.47-.15-.88-.45-1.18-.84-.03,0-.05,0-.08-.01-.02,0-.05-.01-.08-.01-.07,0-.13,0-.19.04-.04.02-.07.05-.1.09-.04.04-.07.09-.11.13-.08.07-.16.13-.25.18-.39.22-.74.4-1.05.56-.34.16-.72.24-1.1.23-.6,0-1.2-.11-1.76-.33-.54-.21-1.04-.51-1.49-.87-.45-.37-.85-.8-1.19-1.28-.34-.48-.62-1.01-.82-1.57-.01-.06-.03-.11-.05-.16-.03-.09-.07-.19-.11-.3-.04-.11-.07-.22-.1-.33-.02-.07-.03-.14-.04-.22v-3.06c.01-.16.05-.32.13-.47.08-.19.14-.33.18-.42l.11-.34c.06-.18.12-.38.18-.59.06-.22.12-.42.18-.61.06-.19.1-.3.11-.35.04-.06.07-.11.1-.18l2.07-2.1c.11-.11.24-.18.39-.23.17-.05.34-.09.52-.11.19-.03.37-.04.54-.05.18,0,.34-.01.49-.01.36,0,.73.03,1.09.1M124.67,52.6c.11.53.3,1.04.57,1.51.25.45.61.83,1.04,1.11.46.3,1.01.45,1.56.43.47,0,.92-.16,1.28-.47.37-.32.68-.71.91-1.14.25-.45.43-.94.54-1.44.11-.45.17-.91.18-1.38,0-.52-.07-1.04-.19-1.54-.12-.53-.32-1.03-.59-1.49-.26-.45-.62-.83-1.04-1.14-.44-.31-.96-.47-1.49-.46-.51-.02-1,.15-1.4.46-.38.3-.69.68-.91,1.11-.23.45-.39.94-.48,1.44-.09.47-.13.94-.14,1.42,0,.53.06,1.06.18,1.58"/>
            <path class="cls-1" d="M115.37,42.22c1.32-9.21,2.16-15.28,2.52-18.19.79,6.8,1.7,12.86,2.73,18.19h6.69c-.39-.42-.59-.98-.56-1.55-.01-.49.19-.97.55-1.31.35-.32.81-.5,1.28-.49h3.06c.21,0,.43.04.63.12l3.64-31.47h-8.77c-1.17,8.03-2,16.25-2.52,24.65l-1-13.2c-.53-6.61-.86-10.43-.98-11.44h-9.37c-1.33,9.82-2.16,17.75-2.49,23.81l-.92-11.7-.95-12.11h-8.77l4.04,34.7h11.2Z"/>
            <path class="cls-1" d="M136.54,56.4c.02-.15.07-.29.14-.42v-2.98c0-.22.1-.44.28-.57.17-.14.39-.21.61-.22.11,0,.23.01.34.04.12.02.23.08.32.16.12.14.19.31.2.49.01.18.07.35.18.49.22.3.43.6.65.87.2.27.44.52.7.73.25.21.54.38.85.51.34.13.71.2,1.07.19.36,0,.72-.04,1.07-.11.35-.07.68-.19.99-.37.29-.17.54-.4.73-.67.2-.3.3-.66.29-1.03.01-.24-.04-.47-.14-.68-.14-.2-.34-.35-.57-.43-.54-.17-1.09-.29-1.64-.35-.57-.07-1.14-.13-1.7-.2-.54-.06-1.08-.16-1.61-.28-.48-.1-.94-.3-1.34-.58-.39-.29-.71-.67-.91-1.11-.25-.59-.37-1.23-.34-1.87-.04-.76.19-1.51.65-2.12.51-.58,1.14-1.02,1.86-1.29.19-.09.39-.16.59-.22.21-.06.43-.09.65-.09h.23c.63.09,1.25.22,1.86.39.61.17,1.19.44,1.71.8.12-.08.21-.18.29-.3.08-.12.15-.23.23-.33.07-.1.16-.18.27-.25.12-.07.26-.11.4-.1.24,0,.47.1.65.27.18.15.28.38.27.62,0,.1,0,.28.01.54,0,.26.02.54.03.84s.02.57.04.84.03.44.03.54v.3c0,.33-.1.64-.27.92-.15.3-.46.48-.8.47-.3,0-.57-.15-.72-.41-.19-.29-.35-.6-.49-.92-.15-.35-.33-.68-.53-1-.19-.31-.5-.54-.86-.63-.24-.07-.42-.11-.54-.14-.13-.03-.25-.05-.37-.06-.13-.02-.25-.03-.38-.03h-.56c-.07,0-.22,0-.45.01-.24,0-.4.01-.48.01-.16.05-.31.13-.45.23-.15.1-.29.23-.42.37-.12.14-.22.3-.3.47-.07.15-.11.31-.11.48-.02.32.08.64.29.89.22.22.5.37.8.43.97-.03,1.93.03,2.89.2.95.16,1.87.42,2.76.78.44.41.82.88,1.13,1.4.31.52.47,1.12.47,1.73,0,.61-.13,1.22-.41,1.77-.26.53-.63,1.01-1.07,1.4-.45.4-.96.71-1.52.92-.56.22-1.16.33-1.76.33-.1,0-.24,0-.41-.01h-.28c-.07-.02-.14-.03-.21-.05-.11-.03-.23-.05-.35-.09s-.25-.07-.37-.1-.2-.06-.25-.08c-.31-.09-.59-.26-.81-.49-.23-.24-.49-.44-.78-.59-.11.07-.19.18-.21.32-.04.16-.09.33-.15.51-.06.18-.16.34-.3.47-.19.15-.43.22-.67.2-.32.03-.62-.15-.75-.44-.12-.28-.19-.59-.19-.9,0-.17,0-.31.01-.42"/>
            <path class="cls-1" d="M78.83,25.89c-.99-.94-2.25-1.56-3.6-1.78,1.97-.19,3.29-.74,3.97-1.67s1.01-2.74,1.01-5.44c0-2.96-.47-5.11-1.42-6.46-.86-1.29-2.18-2.19-3.69-2.52-2.86-.42-5.76-.58-8.65-.49h-6.39v5.94h9.02c.76-.08,1.52.1,2.15.51.48.61.69,1.39.59,2.17v1.91c0,1.53-.2,2.43-.61,2.71-.66.33-1.4.48-2.13.42v-6.45h-9.02v27.49h9.02v-15.65c1.24,0,2.01.22,2.3.66.4,1.1.55,2.28.44,3.45v11.53h8.38v-9.15c.06-1.71-.01-3.43-.22-5.13-.23-.76-.62-1.46-1.15-2.05"/>
            <path class="cls-1" d="M194.68,13.15c-.21-1.14-.73-2.19-1.5-3.05-.94-1-2.16-1.68-3.5-1.97-2.92-.51-5.88-.71-8.84-.6h-6.75v5.94h9.02c.69-.05,1.38.05,2.01.31.36.21.61.57.69.97.13,1,.18,2.01.15,3.02v13.48c0,2.31-.15,3.73-.45,4.25-.3.52-1.1.78-2.4.78V14.73h-9.02v27.49h11.38c1.62.04,3.24-.08,4.84-.35.99-.2,1.91-.62,2.7-1.24.76-.64,1.29-1.5,1.53-2.46.38-2.06.52-4.15.44-6.25v-12.15c.06-2.21-.04-4.42-.3-6.61"/>
            <path class="cls-1" d="M26.51,26.19c-4.1,0-7.43,3.32-7.43,7.43,0,4.1,3.32,7.43,7.43,7.43s7.43-3.32,7.43-7.43h0c0-4.1-3.32-7.43-7.43-7.43M26.51,35.55c-1.07,0-1.93-.87-1.93-1.93,0-1.07.87-1.93,1.93-1.93,1.07,0,1.93.87,1.93,1.93h0c0,1.07-.87,1.93-1.93,1.93"/>
            <path class="cls-1" d="M26.51,19.82V0l-13.98,9.91,13.98,9.91Z"/>
            <path class="cls-1" d="M34.99,42.81l.77.85c5.1-4.44,6.31-11.89,2.88-17.71l-.99.57c3.16,5.36,2.04,12.21-2.66,16.29"/>
            <path class="cls-1" d="M13.58,33.07c0-3.78,1.67-7.37,4.55-9.82l-.96-.68c-5.81,5.14-6.35,14.01-1.22,19.82,1.19,1.34,2.63,2.45,4.23,3.25l.34.17v-1.28l-.04-.02c-4.24-2.23-6.9-6.64-6.9-11.43"/>
            <path class="cls-1" d="M34.14,8.24c-1.47-.44-2.97-.75-4.49-.93v7.77c10.29,1.73,17.23,11.48,15.5,21.77s-11.48,17.23-21.77,15.5S6.15,40.88,7.88,30.59c.68-4.06,2.67-7.78,5.66-10.6l-6.35-4.5c-10.03,10.67-9.5,27.45,1.17,37.48,10.67,10.03,27.45,9.5,37.48-1.17,10.03-10.67,9.5-27.45-1.17-37.48-2.99-2.81-6.6-4.89-10.53-6.07"/>
          </svg>
        </a>
        <a href="mailto:<?php echo get_theme_mod('customTheme-main-callout-mail') ?>" target="_blank"><?php echo get_theme_mod('customTheme-main-callout-mail') ?></a>
      </div>
      <div class="col">
        <h3 class="smallTitle">Adres</h3>
        <div class="text"><p>
          <?php echo get_theme_mod('customTheme-main-callout-street'); ?>,</br>
          <?php echo get_theme_mod('customTheme-main-callout-city'); ?></p>
        </div>
        <div class="partnersList">
          <?php
          $partners = get_field('partners_list', 'option');
          if ($partners && count($partners) > 0) :
            foreach ($partners as $partner) :
              if (!empty($partner['partner_image'])) : ?>
                <div class="partner-item">
                  <?php if (!empty($partner['partner_url'])) : ?>
                    <a class="partner" href="<?php echo esc_url($partner['partner_url']); ?>" title="<?php echo esc_attr($partner['partner_name']); ?>" target="_blank" rel="noopener">
                  <?php endif; ?>
                    <img src="<?php echo esc_url($partner['partner_image']['url']); ?>" alt="<?php echo esc_attr($partner['partner_name']); ?>">
                  <?php if (!empty($partner['partner_url'])) : ?>
                    </a>
                  <?php endif; ?>
                </div>
              <?php endif;
            endforeach;
          endif; ?>
        </div>
      </div>
      <div class="col">
        <h3 class="smallTitle">Contact</h3>
        <div class="text">
          <?php echo get_theme_mod('customTheme-main-callout-contact-details') ?>
        </div>
        <div class="socials">
            <?php if(get_theme_mod('customTheme-main-callout-telephone')): ?><a class="social" title="<?php echo get_theme_mod('customTheme-main-callout-telephone-label') ?>" href="tel:<?php echo get_theme_mod('customTheme-main-callout-telephone') ?>" target="_blank"><i class="icon-phone"></i></a><?php endif; ?>
            <?php if (get_theme_mod('customTheme-main-callout-mail')): ?><a class="social" title="<?php echo get_theme_mod('customTheme-main-callout-mail') ?>" href="mailto:<?php echo get_theme_mod('customTheme-main-callout-mail') ?>" target="_blank"><i class="icon-mail"></i></a><?php endif; ?>
            <?php if (get_theme_mod('customTheme-main-callout-instagram')): ?><a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram', 'https://www.instagram.com')); ?>" title="instagram" target="_blank"><i class="icon-insta"></i></a><?php endif; ?>
            <?php if (get_theme_mod('customTheme-main-callout-tiktok')): ?><a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok', 'https://www.tiktok.com')); ?>" title="tiktok" target="_blank"><i class="icon-tiktok"></i></a><?php endif; ?>
          </div>
      </div>
      <div class="col">
        <h3 class="smallTitle"><?php echo get_theme_mod('customTheme-main-callout-title'); ?></h3>
        <div class="innerMenu">
            <?php wp_nav_menu( array(
              'menu' => 'primary-menu',
            ) ); ?>
        </div>
      </div>
    </div>
    <div class="bottomFooter">
      <div class="col">
        &copy; <?php echo $year; ?> <?php echo get_theme_mod('customTheme-main-callout-title'); ?>
      </div>
      <div class="col">
        <div class="signatureDD">
          <a class="linkDD" href="https://www.doordennis.nl" title="Door Dennis" target="_blank">
            <span class="innerTextDD">Website</span>
            <span class="svgWrap">
              <svg width="71.522" height="11.231" viewBox="0 0 71.522 11.231">
                <defs>
                  <clipPath>
                    <rect width="71.522" height="11.232" fill="none"/>
                  </clipPath>
                </defs>
                <g transform="translate(-771 -850)">
                  <g transform="translate(771 850)">
                    <g transform="translate(0 0)" clip-path="url(#clip-path)">
                      <path d="M22.767,22.25a.2.2,0,0,0-.17.1,2.564,2.564,0,0,1-2.174,1.207H10.36a.006.006,0,0,1-.006-.006V22.42a.191.191,0,0,0-.2-.19,1.7,1.7,0,0,0-1.609,1.338l0,0V24.72a.186.186,0,0,0,.186.186l6.443,0h5.25a3.922,3.922,0,0,0,3.6-2.376.2.2,0,0,0-.185-.277Z" transform="translate(-5.289 -13.764)" fill="#161615"/>
                      <path d="M18.873.2H3.936a.2.2,0,0,0-.2.2v.959a.2.2,0,0,0,.2.2h.436C6.982,1.63,7,2.227,7,3.421a.006.006,0,0,1-.006.006H.2a.2.2,0,0,0-.2.2v.959a.2.2,0,0,0,.2.2h.26c2.809.058,2.8.66,2.8,1.906a.006.006,0,0,1-.006.006H2.386A2.324,2.324,0,0,0,.025,9.17a2.3,2.3,0,0,0,2.29,2.113h.173a.2.2,0,0,0,.2-.2v-.959a.2.2,0,0,0-.2-.2H2.315a.947.947,0,0,1,0-1.894H17.6l1.386,0h0A3.919,3.919,0,0,0,18.873.2M9.883,6.689H8.811A.006.006,0,0,1,8.8,6.683V5.521a.191.191,0,0,0-.2-.19A1.6,1.6,0,0,0,7,6.689V6.683a.006.006,0,0,1-.006.006H5.071a.006.006,0,0,1-.006-.006V5.974c0-.879-.1-1.16.437-1.2h9.631a2.571,2.571,0,0,1,2.478,***********,0,0,1-.006.008ZM19,6.683a.007.007,0,0,1-.006,0,3.924,3.924,0,0,0-3.861-3.251H8.811A.006.006,0,0,1,8.8,3.421V2.745c0-.853-.1-1.143.392-1.193h9.611a2.611,2.611,0,0,1,2.63,2.434A2.569,2.569,0,0,1,19,6.683" transform="translate(0 -0.125)" fill="#161615"/>
                      <path d="M72.556,5.345V.234h2.853a4.062,4.062,0,0,1,1.627.3,2.28,2.28,0,0,1,1.047.869,2.535,2.535,0,0,1,.364,1.389,2.532,2.532,0,0,1-.364,1.393,2.287,2.287,0,0,1-1.047.865,4.062,4.062,0,0,1-1.627.3Zm1.508-1.122H75.32a2.07,2.07,0,0,0,.642-.093,1.5,1.5,0,0,0,.494-.264,1.164,1.164,0,0,0,.323-.423,1.362,1.362,0,0,0,.115-.572V2.7a1.337,1.337,0,0,0-.115-.568,1.177,1.177,0,0,0-.323-.42,1.494,1.494,0,0,0-.494-.264,2.07,2.07,0,0,0-.642-.093H74.064Z" transform="translate(-44.924 -0.145)" fill="#161615"/>
                      <path d="M93.52,5.289a4.585,4.585,0,0,1-1.779-.316,2.58,2.58,0,0,1-1.163-.906,2.437,2.437,0,0,1-.408-1.422,2.439,2.439,0,0,1,.408-1.422A2.582,2.582,0,0,1,91.741.316a5.2,5.2,0,0,1,3.57,0,2.587,2.587,0,0,1,1.159.906,2.439,2.439,0,0,1,.408,1.422,2.437,2.437,0,0,1-.408,1.422,2.585,2.585,0,0,1-1.159.906,4.6,4.6,0,0,1-1.79.316m0-1.122a2.6,2.6,0,0,0,.732-.1,1.677,1.677,0,0,0,.568-.282,1.27,1.27,0,0,0,.368-.457,1.384,1.384,0,0,0,.13-.605V2.548a1.343,1.343,0,0,0-.13-.594A1.28,1.28,0,0,0,94.82,1.5a1.677,1.677,0,0,0-.568-.283,2.619,2.619,0,0,0-.732-.1,2.574,2.574,0,0,0-.728.1,1.722,1.722,0,0,0-.569.283,1.229,1.229,0,0,0-.368.453,1.374,1.374,0,0,0-.126.594v.178a1.415,1.415,0,0,0,.126.605,1.22,1.22,0,0,0,.368.457,1.722,1.722,0,0,0,.569.282,2.56,2.56,0,0,0,.728.1" transform="translate(-55.829 0)" fill="#161615"/>
                      <path d="M113.278,5.289a4.583,4.583,0,0,1-1.779-.316,2.58,2.58,0,0,1-1.163-.906,2.437,2.437,0,0,1-.408-1.422,2.439,2.439,0,0,1,.408-1.422A2.582,2.582,0,0,1,111.5.316a5.2,5.2,0,0,1,3.569,0,2.579,2.579,0,0,1,1.159.906,2.436,2.436,0,0,1,.409,1.422,2.435,2.435,0,0,1-.409,1.422,2.577,2.577,0,0,1-1.159.906,4.6,4.6,0,0,1-1.79.316m0-1.122a2.6,2.6,0,0,0,.732-.1,1.677,1.677,0,0,0,.568-.282,1.27,1.27,0,0,0,.368-.457,1.383,1.383,0,0,0,.129-.605V2.548a1.343,1.343,0,0,0-.129-.594,1.28,1.28,0,0,0-.368-.453,1.677,1.677,0,0,0-.568-.283,2.619,2.619,0,0,0-.732-.1,2.579,2.579,0,0,0-.728.1,1.724,1.724,0,0,0-.568.283,1.225,1.225,0,0,0-.368.453,1.374,1.374,0,0,0-.126.594v.178a1.415,1.415,0,0,0,.126.605,1.216,1.216,0,0,0,.368.457,1.724,1.724,0,0,0,.568.282,2.565,2.565,0,0,0,.728.1" transform="translate(-68.063 0)" fill="#161615"/>
                      <path d="M130.271,5.345V.234h8.058a1.694,1.694,0,0,1,.921.23,1.49,1.49,0,0,1,.55.606,1.825,1.825,0,0,1,.186.821,1.725,1.725,0,0,1-.23.88,1.6,1.6,0,0,1-.691.628l1.04,1.946H138.4l-.847-1.7h-5.769v1.7Zm1.508-2.815h6.052a.594.594,0,0,0,.439-.163.594.594,0,0,0,.163-.438.616.616,0,0,0-.074-.308.486.486,0,0,0-.208-.2.708.708,0,0,0-.32-.067h-6.052Z" transform="translate(-80.658 -0.145)" fill="#161615"/>
                      <path d="M72.556,20.949V15.838h6.661a4.062,4.062,0,0,1,1.627.3A2.28,2.28,0,0,1,81.891,17a2.839,2.839,0,0,1,0,2.782,2.287,2.287,0,0,1-1.047.865,4.062,4.062,0,0,1-1.627.3Zm1.508-1.122h5.064a2.07,2.07,0,0,0,.642-.093,1.5,1.5,0,0,0,.494-.264,1.164,1.164,0,0,0,.323-.423,1.362,1.362,0,0,0,.115-.572V18.3a1.337,1.337,0,0,0-.115-.568,1.177,1.177,0,0,0-.323-.42,1.494,1.494,0,0,0-.494-.264,2.07,2.07,0,0,0-.642-.093H74.064Z" transform="translate(-44.924 -9.806)" fill="#161615"/>
                      <path d="M100.754,20.949V15.838h9.127V16.96H102.27v.861h7.136v1.085H102.27v.921h7.671v1.122Z" transform="translate(-62.383 -9.806)" fill="#161615"/>
                      <path d="M127.587,20.949V15.838h1.352l2.645,2.385q.067.059.174.163l.223.215c.077.075.142.144.2.208h.067q0-.134,0-.334t0-.357v-2.28h1.427v5.111h-1.33l-2.585-2.332q-.178-.157-.371-.35t-.312-.3h-.059q0,.1,0,.312t0,.461v2.213Z" transform="translate(-78.996 -9.806)" fill="#161615"/>
                      <path d="M146.859,20.949V15.838h1.352l2.645,2.385q.067.059.174.163l.223.215c.077.075.142.144.2.208h.067q0-.134,0-.334t0-.357v-2.28h1.427v5.111h-1.33l-2.585-2.332q-.178-.157-.371-.35t-.312-.3h-.059q0,.1,0,.312t0,.461v2.213Z" transform="translate(-90.929 -9.806)" fill="#161615"/>
                      <rect width="1.508" height="5.111" transform="translate(63.27 6.032)" fill="#161615"/>
                      <path d="M175.709,20.893a8.611,8.611,0,0,1-1.167-.074,3.412,3.412,0,0,1-.951-.26,1.518,1.518,0,0,1-.639-.52,1.461,1.461,0,0,1-.23-.847v-.037a.14.14,0,0,1,.007-.045h1.486a.467.467,0,0,0-.011.059.512.512,0,0,0,0,.067.425.425,0,0,0,.174.36,1.118,1.118,0,0,0,.487.182,4.536,4.536,0,0,0,.735.052q.171,0,.353-.011t.353-.037a1.508,1.508,0,0,0,.308-.078.6.6,0,0,0,.215-.13.267.267,0,0,0,.078-.2.279.279,0,0,0-.167-.249,1.584,1.584,0,0,0-.46-.149q-.294-.055-.654-.1t-.746-.108a5.986,5.986,0,0,1-.75-.171,2.7,2.7,0,0,1-.654-.286,1.361,1.361,0,0,1-.461-.461,1.3,1.3,0,0,1-.171-.683,1.2,1.2,0,0,1,.2-.7,1.561,1.561,0,0,1,.576-.486,3.121,3.121,0,0,1,.876-.286,6.077,6.077,0,0,1,1.1-.093,5.775,5.775,0,0,1,1.1.1,3,3,0,0,1,.847.286,1.459,1.459,0,0,1,.542.475,1.189,1.189,0,0,1,.19.673v.1h-1.464v-.059a.37.37,0,0,0-.134-.278.936.936,0,0,0-.39-.193,2.374,2.374,0,0,0-.62-.07,3.817,3.817,0,0,0-.721.056,1.135,1.135,0,0,0-.416.149.24.24,0,0,0,.037.446,1.729,1.729,0,0,0,.46.137q.29.052.65.1t.751.1a5.933,5.933,0,0,1,.75.163,2.718,2.718,0,0,1,.65.278,1.378,1.378,0,0,1,.461.446,1.228,1.228,0,0,1,.171.665,1.393,1.393,0,0,1-.356,1,2.06,2.06,0,0,1-.977.55,5.39,5.39,0,0,1-1.422.171" transform="translate(-106.943 -9.661)" fill="#161615"/>
                    </g>
                  </g>
                </g>
              </svg>
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>
</div>
</body>
</html>

<?php
/**
 * Records Admin Settings
 */

class Rewindrecords_Records_Admin {

    /**
     * API instance
     */
    private $api;

    /**
     * CSV Import instance
     */
    private $csv_import;

    /**
     * Constructor
     */
    public function __construct() {
        // Initialize API
        require_once get_template_directory() . '/includes/class-records-api.php';
        $this->api = new Rewindrecords_Records_API();

        // Initialize CSV Import
        require_once get_template_directory() . '/includes/class-records-csv-import.php';
        $this->csv_import = new Rewindrecords_CSV_Import();

        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Register settings
        add_action('admin_init', array($this, 'register_settings'));

        // Add AJAX handlers
        add_action('wp_ajax_rewindrecords_test_api_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_rewindrecords_import_collection', array($this, 'ajax_import_collection'));
        add_action('wp_ajax_rewindrecords_update_record_data', array($this, 'ajax_update_record_data'));
        add_action('wp_ajax_rewindrecords_get_all_records', array($this, 'ajax_get_all_records'));
        add_action('wp_ajax_rewindrecords_update_record_release_date', array($this, 'ajax_update_record_release_date'));

        // Add custom columns to record post type
        add_filter('manage_record_posts_columns', array($this, 'add_record_columns'));
        add_action('manage_record_posts_custom_column', array($this, 'display_record_column_content'), 10, 2);
        add_filter('manage_edit-record_sortable_columns', array($this, 'make_record_columns_sortable'));

        // Add quick edit fields
        add_action('quick_edit_custom_box', array($this, 'add_quick_edit_fields'), 10, 2);
        add_action('admin_footer-edit.php', array($this, 'add_quick_edit_javascript'));

        // Add bulk edit fields
        add_action('bulk_edit_custom_box', array($this, 'add_bulk_edit_fields'), 10, 2);

        // Save quick edit and bulk edit data
        add_action('save_post_record', array($this, 'save_record_data'), 10, 2);
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Rewindrecords', 'rewindrecords'),
            __('Rewindrecords', 'rewindrecords'),
            'manage_options',
            'rewindrecords',
            array($this, 'display_settings_page'),
            'dashicons-album',
            30
        );

        add_submenu_page(
            'rewindrecords',
            __('API Settings', 'rewindrecords'),
            __('API Settings', 'rewindrecords'),
            'manage_options',
            'rewindrecords',
            array($this, 'display_settings_page')
        );

        add_submenu_page(
            'rewindrecords',
            __('Import Collection', 'rewindrecords'),
            __('Import Collection', 'rewindrecords'),
            'manage_options',
            'rewindrecords-import',
            array($this, 'display_import_page')
        );

        add_submenu_page(
            'rewindrecords',
            __('CSV Import', 'rewindrecords'),
            __('CSV Import', 'rewindrecords'),
            'manage_options',
            'rewindrecords-csv-import',
            array($this, 'display_csv_import_page')
        );

        add_submenu_page(
            'rewindrecords',
            __('Update Release Dates', 'rewindrecords'),
            __('Update Release Dates', 'rewindrecords'),
            'manage_options',
            'rewindrecords-update-dates',
            array($this, 'display_update_dates_page')
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('rewindrecords_api', 'rewindrecords_api_token');
        register_setting('rewindrecords_api', 'rewindrecords_api_username');

        add_settings_section(
            'rewindrecords_api_section',
            __('API Settings', 'rewindrecords'),
            array($this, 'api_section_callback'),
            'rewindrecords_api'
        );

        add_settings_field(
            'rewindrecords_api_token',
            __('Personal Access Token', 'rewindrecords'),
            array($this, 'token_field_callback'),
            'rewindrecords_api',
            'rewindrecords_api_section'
        );

        add_settings_field(
            'rewindrecords_api_username',
            __('Username', 'rewindrecords'),
            array($this, 'username_field_callback'),
            'rewindrecords_api',
            'rewindrecords_api_section'
        );
    }

    /**
     * API section description
     */
    public function api_section_callback() {
        echo '<p>' . __('Connect to the API to import your record collection.', 'rewindrecords') . '</p>';
        echo '<p>' . __('Enter your API credentials below.', 'rewindrecords') . '</p>';
    }

    /**
     * Token field callback
     */
    public function token_field_callback() {
        $token = $this->api->get_token();
        echo '<input type="text" name="rewindrecords_api_token" value="' . esc_attr($token) . '" class="regular-text">';
        echo '<p class="description">' . __('Your API personal access token', 'rewindrecords') . '</p>';
        echo '<button type="button" class="button" id="test-api-connection">' . __('Test Connection', 'rewindrecords') . '</button>';
        echo '<span id="connection-result" style="margin-left: 10px;"></span>';
    }

    /**
     * Username field callback
     */
    public function username_field_callback() {
        $username = get_option('rewindrecords_api_username', '');
        echo '<input type="text" name="rewindrecords_api_username" value="' . esc_attr($username) . '" class="regular-text">';
        echo '<p class="description">' . __('Your API username', 'rewindrecords') . '</p>';
    }

    /**
     * Display settings page
     */
    public function display_settings_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            <form action="options.php" method="post">
                <?php
                settings_fields('rewindrecords_api');
                do_settings_sections('rewindrecords_api');
                submit_button(__('Save Settings', 'rewindrecords'));
                ?>
            </form>
        </div>

        <script>
            jQuery(document).ready(function($) {
                $('#test-api-connection').on('click', function() {
                    var token = $('input[name="rewindrecords_api_token"]').val();
                    var $result = $('#connection-result');

                    if (!token) {
                        $result.html('<span style="color: red;"><?php _e('Please enter a token first', 'rewindrecords'); ?></span>');
                        return;
                    }

                    $result.html('<span style="color: blue;"><?php _e('Testing...', 'rewindrecords'); ?></span>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'rewindrecords_test_api_connection',
                            token: token,
                            nonce: '<?php echo wp_create_nonce('rewindrecords_api'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $result.html('<span style="color: green;"><?php _e('Connection successful!', 'rewindrecords'); ?></span>');
                            } else {
                                $result.html('<span style="color: red;">' + response.data + '</span>');
                            }
                        },
                        error: function() {
                            $result.html('<span style="color: red;"><?php _e('Connection failed', 'rewindrecords'); ?></span>');
                        }
                    });
                });
            });
        </script>
        <?php
    }

    /**
     * Display import page
     */
    public function display_import_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        $token = $this->api->get_token();
        $username = get_option('rewindrecords_api_username', '');

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <?php if (empty($token) || empty($username)): ?>
                <div class="notice notice-error">
                    <p><?php _e('Please configure your API settings first.', 'rewindrecords'); ?></p>
                    <p><a href="<?php echo admin_url('admin.php?page=rewindrecords'); ?>" class="button"><?php _e('Go to Settings', 'rewindrecords'); ?></a></p>
                </div>
            <?php else: ?>
                <div class="card">
                    <h2><?php _e('Import Collection', 'rewindrecords'); ?></h2>
                    <p><?php _e('This will import your collection into WordPress as Record posts.', 'rewindrecords'); ?></p>
                    <p><?php _e('The import process may take some time depending on the size of your collection.', 'rewindrecords'); ?></p>

                    <div id="import-controls">
                        <button type="button" class="button button-primary" id="start-import"><?php _e('Start Import', 'rewindrecords'); ?></button>
                    </div>

                    <div id="import-progress" style="display: none; margin-top: 20px;">
                        <div class="progress-bar" style="height: 20px; background-color: #f1f1f1; margin-bottom: 10px;">
                            <div class="progress" style="width: 0%; height: 100%; background-color: #0073aa;"></div>
                        </div>
                        <p class="progress-status"><?php _e('Preparing import...', 'rewindrecords'); ?></p>
                    </div>

                    <div id="import-results" style="display: none; margin-top: 20px;">
                        <h3><?php _e('Import Results', 'rewindrecords'); ?></h3>
                        <p class="import-summary"></p>
                        <div class="import-details" style="margin-top: 10px;">
                            <p><strong><?php _e('New records:', 'rewindrecords'); ?></strong> <span class="new-records-count">0</span></p>
                            <p><strong><?php _e('Updated records:', 'rewindrecords'); ?></strong> <span class="updated-records-count">0</span></p>
                            <p><strong><?php _e('Total processed:', 'rewindrecords'); ?></strong> <span class="total-records-count">0</span></p>
                        </div>
                        <div class="import-errors" style="display: none; margin-top: 15px;">
                            <h4><?php _e('Errors', 'rewindrecords'); ?></h4>
                            <ul class="error-list"></ul>
                        </div>
                    </div>
                </div>

                <script>
                    jQuery(document).ready(function($) {
                        $('#start-import').on('click', function() {
                            var $controls = $('#import-controls');
                            var $progress = $('#import-progress');
                            var $results = $('#import-results');
                            var $status = $progress.find('.progress-status');
                            var $progressBar = $progress.find('.progress');

                            // Reset UI
                            $controls.hide();
                            $results.hide();
                            $progress.show();
                            $progressBar.css('width', '0%');

                            // Start import
                            $.ajax({
                                url: ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'rewindrecords_import_collection',
                                    nonce: '<?php echo wp_create_nonce('rewindrecords_api'); ?>'
                                },
                                success: function(response) {
                                    $progress.hide();
                                    $results.show();

                                    if (response.success) {
                                        var data = response.data;
                                        $('.import-summary').text(data.message);

                                        // Update counts
                                        $('.new-records-count').text(data.total_imported);
                                        $('.updated-records-count').text(data.total_updated);
                                        $('.total-records-count').text(data.total);

                                        if (data.errors && data.errors.length > 0) {
                                            var $errorList = $('.error-list');
                                            $errorList.empty();

                                            $.each(data.errors, function(i, error) {
                                                $errorList.append('<li>' + error + '</li>');
                                            });

                                            $('.import-errors').show();
                                        } else {
                                            $('.import-errors').hide();
                                        }
                                    } else {
                                        $('.import-summary').text(response.data);
                                        $('.import-errors').hide();
                                    }

                                    $controls.show();
                                },
                                error: function() {
                                    $progress.hide();
                                    $results.show();
                                    $('.import-summary').text('<?php _e('Import failed. Please try again.', 'rewindrecords'); ?>');
                                    $('.import-errors').hide();
                                    $controls.show();
                                }
                            });
                        });
                    });
                </script>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Display CSV import page
     */
    public function display_csv_import_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <div class="card">
                <h2><?php _e('Import Records from CSV', 'rewindrecords'); ?></h2>
                <p><?php _e('Upload a CSV file to import records into your collection.', 'rewindrecords'); ?></p>
                <p><?php _e('The following information will be imported from the CSV file:', 'rewindrecords'); ?></p>
                <ul>
                    <li><?php _e('Article number, description, and barcode', 'rewindrecords'); ?></li>
                    <li><?php _e('Price (including VAT) which will be displayed on the site', 'rewindrecords'); ?></li>
                    <li><?php _e('Stock quantity for inventory management', 'rewindrecords'); ?></li>
                    <li><?php _e('Category information', 'rewindrecords'); ?></li>
                </ul>
                <p><?php _e('Additionally, the following information will be automatically retrieved based on artist and album title:', 'rewindrecords'); ?></p>
                <ul>
                    <li><?php _e('Album covers (high resolution) via iTunes API or Cover Art Archive', 'rewindrecords'); ?></li>
                    <li><?php _e('Complete tracklist with track numbers and durations (via MusicBrainz)', 'rewindrecords'); ?></li>
                    <li><?php _e('Album description with genre, release date, label, and other metadata in Dutch', 'rewindrecords'); ?></li>
                </ul>
                <p><?php _e('The system will first try to find album covers via iTunes API, and if not found, will use MusicBrainz + Cover Art Archive as a fallback.', 'rewindrecords'); ?></p>
                <p><?php _e('The CSV file should have the following format:', 'rewindrecords'); ?></p>
                <pre>Artikelnummer,Artikel omschrijving,Artikelgroep,Barcode,Prijs incl,Inkoop prijs ex,Btw hoog/laag,Artikel voorraad</pre>

                <form id="csv-import-form" enctype="multipart/form-data">
                    <p>
                        <label for="csv-file"><?php _e('Select CSV File:', 'rewindrecords'); ?></label>
                        <input type="file" name="csv_file" id="csv-file" accept=".csv" required>
                    </p>

                    <div id="csv-import-controls">
                        <button type="submit" class="button button-primary" id="start-csv-import"><?php _e('Import Records', 'rewindrecords'); ?></button>
                    </div>
                </form>

                <div id="csv-import-progress" style="display: none; margin-top: 20px;">
                    <p class="progress-status"><?php _e('Importing records...', 'rewindrecords'); ?></p>
                </div>

                <div id="csv-import-results" style="display: none; margin-top: 20px;">
                    <h3><?php _e('Import Results', 'rewindrecords'); ?></h3>
                    <p class="import-summary"></p>
                    <div class="import-details" style="margin-top: 10px;">
                        <p><strong><?php _e('New records:', 'rewindrecords'); ?></strong> <span class="new-records-count">0</span></p>
                        <p><strong><?php _e('Updated records:', 'rewindrecords'); ?></strong> <span class="updated-records-count">0</span></p>
                        <p><strong><?php _e('Total processed:', 'rewindrecords'); ?></strong> <span class="total-records-count">0</span></p>
                    </div>
                    <div class="import-errors" style="display: none; margin-top: 15px;">
                        <h4><?php _e('Errors', 'rewindrecords'); ?></h4>
                        <ul class="error-list"></ul>
                    </div>
                </div>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    $('#csv-import-form').on('submit', function(e) {
                        e.preventDefault();

                        var $form = $(this);
                        var formData = new FormData($form[0]);
                        formData.append('action', 'rewindrecords_import_csv');
                        formData.append('nonce', '<?php echo wp_create_nonce('rewindrecords_csv_import'); ?>');

                        var $controls = $('#csv-import-controls');
                        var $progress = $('#csv-import-progress');
                        var $results = $('#csv-import-results');

                        // Reset UI
                        $controls.hide();
                        $results.hide();
                        $progress.show();

                        // Start import
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(response) {
                                $progress.hide();
                                $results.show();

                                if (response.success) {
                                    var data = response.data;
                                    $('.import-summary').text(data.message);

                                    // Update counts
                                    $('.new-records-count').text(data.total_imported);
                                    $('.updated-records-count').text(data.total_updated);
                                    $('.total-records-count').text(data.total);

                                    if (data.errors && data.errors.length > 0) {
                                        var $errorList = $('.error-list');
                                        $errorList.empty();

                                        $.each(data.errors, function(i, error) {
                                            $errorList.append('<li>' + error + '</li>');
                                        });

                                        $('.import-errors').show();
                                    } else {
                                        $('.import-errors').hide();
                                    }
                                } else {
                                    $('.import-summary').text(response.data);
                                    $('.import-errors').hide();
                                }

                                $controls.show();
                            },
                            error: function() {
                                $progress.hide();
                                $results.show();
                                $('.import-summary').text('<?php _e('Import failed. Please try again.', 'rewindrecords'); ?>');
                                $('.import-errors').hide();
                                $controls.show();
                            }
                        });
                    });
                });
            </script>
        </div>
        <?php
    }

    /**
     * AJAX handler for testing API connection
     */
    public function ajax_test_connection() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_api')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Get token
        $token = isset($_POST['token']) ? sanitize_text_field($_POST['token']) : '';

        if (empty($token)) {
            wp_send_json_error(__('Token is empty', 'rewindrecords'));
        }

        // Test connection
        $result = $this->api->test_connection($token);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success();
    }

    /**
     * AJAX handler for importing collection
     */
    public function ajax_import_collection() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_api')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Get username
        $username = get_option('rewindrecords_api_username', '');

        if (empty($username)) {
            wp_send_json_error(__('Username is not set', 'rewindrecords'));
        }

        // Import collection
        $result = $this->api->import_collection($username);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        // Format response
        $message = sprintf(
            __('Successfully imported %d new records and updated %d existing records from your collection.', 'rewindrecords'),
            $result['total_imported'],
            $result['total_updated']
        );

        wp_send_json_success(array(
            'message' => $message,
            'total_imported' => $result['total_imported'],
            'total_updated' => $result['total_updated'],
            'total' => $result['total_imported'] + $result['total_updated'],
            'errors' => $result['errors'],
        ));
    }

    /**
     * Add custom columns to record post type
     *
     * @param array $columns Existing columns
     * @return array Modified columns
     */
    public function add_record_columns($columns) {
        $new_columns = array();

        // Insert columns after title
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;

            if ($key === 'title') {
                $new_columns['article_id'] = __('Article ID', 'rewindrecords');
                $new_columns['price'] = __('Price', 'rewindrecords');
                $new_columns['stock'] = __('Stock', 'rewindrecords');
                $new_columns['product_type'] = __('Product Type', 'rewindrecords');
            }
        }

        return $new_columns;
    }

    /**
     * Display content for custom columns
     *
     * @param string $column Column name
     * @param int $post_id Post ID
     */
    public function display_record_column_content($column, $post_id) {
        switch ($column) {
            case 'article_id':
                $article_id = get_post_meta($post_id, '_record_article_id', true);
                echo esc_html($article_id);
                break;

            case 'price':
                $price = get_post_meta($post_id, '_record_price', true);
                if (!empty($price)) {
                    echo '€ ' . number_format((float)$price, 2, ',', '.');
                } else {
                    echo '—';
                }
                break;

            case 'stock':
                $stock = get_post_meta($post_id, '_record_stock', true);
                if ($stock !== '') {
                    echo esc_html($stock);
                } else {
                    echo '0';
                }
                break;

            case 'product_type':
                $terms = get_the_terms($post_id, 'record_product_type');
                if ($terms && !is_wp_error($terms)) {
                    $product_types = array();
                    foreach ($terms as $term) {
                        $product_types[] = $term->name;
                    }
                    echo esc_html(implode(', ', $product_types));
                } else {
                    echo __('Album', 'rewindrecords'); // Default to Album if not set
                }
                break;
        }
    }

    /**
     * Make custom columns sortable
     *
     * @param array $columns Sortable columns
     * @return array Modified sortable columns
     */
    public function make_record_columns_sortable($columns) {
        $columns['article_id'] = 'article_id';
        $columns['price'] = 'price';
        $columns['stock'] = 'stock';
        $columns['product_type'] = 'product_type';

        return $columns;
    }

    /**
     * Add quick edit fields
     *
     * @param string $column_name Column name
     * @param string $post_type Post type
     */
    public function add_quick_edit_fields($column_name, $post_type) {
        if ($post_type !== 'record') {
            return;
        }

        if ($column_name !== 'price') {
            return;
        }

        ?>
        <fieldset class="inline-edit-col-right">
            <div class="inline-edit-col">
                <label>
                    <span class="title"><?php _e('Price', 'rewindrecords'); ?></span>
                    <span class="input-text-wrap">
                        <input type="text" name="record_price" class="regular-text" value="">
                    </span>
                </label>
                <label>
                    <span class="title"><?php _e('Stock', 'rewindrecords'); ?></span>
                    <span class="input-text-wrap">
                        <input type="number" name="record_stock" class="small-text" value="" min="0">
                    </span>
                </label>
                <input type="hidden" name="record_data_nonce" value="<?php echo wp_create_nonce('record_data_nonce'); ?>">
            </div>
        </fieldset>
        <?php
    }

    /**
     * Add bulk edit fields
     *
     * @param string $column_name Column name
     * @param string $post_type Post type
     */
    public function add_bulk_edit_fields($column_name, $post_type) {
        if ($post_type !== 'record') {
            return;
        }

        if ($column_name !== 'price') {
            return;
        }

        ?>
        <fieldset class="inline-edit-col-right">
            <div class="inline-edit-col">
                <label>
                    <span class="title"><?php _e('Price', 'rewindrecords'); ?></span>
                    <span class="input-text-wrap">
                        <input type="text" name="record_price" class="regular-text" value="">
                    </span>
                </label>
                <label>
                    <span class="title"><?php _e('Stock', 'rewindrecords'); ?></span>
                    <span class="input-text-wrap">
                        <input type="number" name="record_stock" class="small-text" value="" min="0">
                    </span>
                </label>
                <input type="hidden" name="record_data_nonce" value="<?php echo wp_create_nonce('record_data_nonce'); ?>">
            </div>
        </fieldset>
        <?php
    }

    /**
     * Add JavaScript for quick edit
     */
    public function add_quick_edit_javascript() {
        global $post_type;

        if ($post_type !== 'record') {
            return;
        }

        ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Quick Edit
                $('.editinline').on('click', function() {
                    var post_id = $(this).closest('tr').attr('id');
                    post_id = post_id.replace('post-', '');

                    var $row = $(this).closest('tr');
                    var price = $row.find('td.price').text().replace('€ ', '').replace('.', '').replace(',', '.');
                    var stock = $row.find('td.stock').text();

                    // Set values in quick edit form
                    $('#edit-' + post_id).find('input[name="record_price"]').val(price);
                    $('#edit-' + post_id).find('input[name="record_stock"]').val(stock);
                });

                // AJAX update for quick edit
                $(document).on('click', '#bulk_edit, .save', function() {
                    var $button = $(this);

                    // If this is not a record post type, return
                    if ($button.closest('form').find('input[name="post_type"]').val() !== 'record') {
                        return;
                    }

                    // If this is a bulk edit
                    if ($button.attr('id') === 'bulk_edit') {
                        var price = $('input[name="record_price"]').val();
                        var stock = $('input[name="record_stock"]').val();

                        // If both fields are empty, return
                        if (price === '' && stock === '') {
                            return;
                        }

                        // Get selected post IDs
                        var post_ids = [];
                        $('input[name="post[]"]:checked').each(function() {
                            post_ids.push($(this).val());
                        });

                        // Update records via AJAX
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_update_record_data',
                                post_ids: post_ids,
                                price: price,
                                stock: stock,
                                nonce: $('input[name="record_data_nonce"]').val()
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Reload the page
                                    location.reload();
                                }
                            }
                        });
                    }
                });
            });
        </script>
        <?php
    }

    /**
     * Save record data from quick edit and bulk edit
     *
     * @param int $post_id Post ID
     * @param WP_Post $post Post object
     */
    public function save_record_data($post_id, $post) {
        // If this is an autosave, our form has not been submitted, so we don't want to do anything
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check if our nonce is set
        if (!isset($_POST['record_data_nonce'])) {
            return;
        }

        // Verify that the nonce is valid
        if (!wp_verify_nonce($_POST['record_data_nonce'], 'record_data_nonce')) {
            return;
        }

        // Check the user's permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save price
        if (isset($_POST['record_price']) && $_POST['record_price'] !== '') {
            $price = sanitize_text_field($_POST['record_price']);
            $price = str_replace(',', '.', $price);
            update_post_meta($post_id, '_record_price', floatval($price));
        }

        // Save stock
        if (isset($_POST['record_stock']) && $_POST['record_stock'] !== '') {
            $stock = intval($_POST['record_stock']);
            update_post_meta($post_id, '_record_stock', $stock);
        }
    }

    /**
     * AJAX handler for updating record data
     */
    public function ajax_update_record_data() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'record_data_nonce')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Check if we have post IDs
        if (!isset($_POST['post_ids']) || !is_array($_POST['post_ids'])) {
            wp_send_json_error(__('No records selected', 'rewindrecords'));
        }

        $post_ids = array_map('intval', $_POST['post_ids']);
        $price = isset($_POST['price']) ? sanitize_text_field($_POST['price']) : '';
        $stock = isset($_POST['stock']) ? intval($_POST['stock']) : '';

        // Update each post
        foreach ($post_ids as $post_id) {
            // Check if the post exists and is a record
            $post = get_post($post_id);
            if (!$post || $post->post_type !== 'record') {
                continue;
            }

            // Update price
            if ($price !== '') {
                $price_clean = str_replace(',', '.', $price);
                update_post_meta($post_id, '_record_price', floatval($price_clean));
            }

            // Update stock
            if ($stock !== '') {
                update_post_meta($post_id, '_record_stock', $stock);
            }
        }

        wp_send_json_success();
    }

    /**
     * Display update dates page
     */
    public function display_update_dates_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <div class="card">
                <h2><?php _e('Update Release Dates for Records', 'rewindrecords'); ?></h2>
                <p><?php _e('This tool will update all records with accurate release dates from iTunes API and MusicBrainz.', 'rewindrecords'); ?></p>
                <p><?php _e('The process will:', 'rewindrecords'); ?></p>
                <ul>
                    <li><?php _e('Scan all records in your collection', 'rewindrecords'); ?></li>
                    <li><?php _e('Try to find accurate release dates from iTunes API first', 'rewindrecords'); ?></li>
                    <li><?php _e('If not found, try MusicBrainz API as a fallback', 'rewindrecords'); ?></li>
                    <li><?php _e('Update the release date metadata for each record', 'rewindrecords'); ?></li>
                </ul>
                <p><?php _e('This will ensure that your "Latest Releases" section shows the most accurate and up-to-date information.', 'rewindrecords'); ?></p>

                <div id="update-dates-controls">
                    <button type="button" class="button button-primary" id="start-update-dates"><?php _e('Update Release Dates', 'rewindrecords'); ?></button>
                </div>

                <div id="update-dates-progress" style="display: none; margin-top: 20px;">
                    <div class="progress-bar" style="height: 20px; background-color: #f1f1f1; margin-bottom: 10px;">
                        <div class="progress" style="width: 0%; height: 100%; background-color: #0073aa;"></div>
                    </div>
                    <p class="progress-status"><?php _e('Updating release dates...', 'rewindrecords'); ?></p>
                    <p class="progress-count"><span class="current">0</span> / <span class="total">0</span> <?php _e('records processed', 'rewindrecords'); ?></p>
                </div>

                <div id="update-dates-results" style="display: none; margin-top: 20px;">
                    <h3><?php _e('Update Results', 'rewindrecords'); ?></h3>
                    <p class="update-summary"></p>
                    <div class="update-details" style="margin-top: 10px;">
                        <p><strong><?php _e('Records updated:', 'rewindrecords'); ?></strong> <span class="updated-records-count">0</span></p>
                        <p><strong><?php _e('Records already up-to-date:', 'rewindrecords'); ?></strong> <span class="unchanged-records-count">0</span></p>
                        <p><strong><?php _e('Total processed:', 'rewindrecords'); ?></strong> <span class="total-records-count">0</span></p>
                    </div>
                </div>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    var recordsToProcess = [];
                    var currentIndex = 0;
                    var totalRecords = 0;
                    var updatedRecords = 0;
                    var unchangedRecords = 0;

                    $('#start-update-dates').on('click', function() {
                        var $controls = $('#update-dates-controls');
                        var $progress = $('#update-dates-progress');
                        var $results = $('#update-dates-results');

                        // Reset UI
                        $controls.hide();
                        $results.hide();
                        $progress.show();

                        // Get all records
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_get_all_records',
                                nonce: '<?php echo wp_create_nonce('rewindrecords_update_dates'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    recordsToProcess = response.data;
                                    totalRecords = recordsToProcess.length;

                                    // Update progress UI
                                    $('.progress-count .total').text(totalRecords);

                                    // Start processing records
                                    processNextRecord();
                                } else {
                                    $progress.hide();
                                    $controls.show();
                                    alert('<?php _e('Error: Could not retrieve records.', 'rewindrecords'); ?>');
                                }
                            },
                            error: function() {
                                $progress.hide();
                                $controls.show();
                                alert('<?php _e('Error: Could not retrieve records.', 'rewindrecords'); ?>');
                            }
                        });
                    });

                    function processNextRecord() {
                        if (currentIndex >= recordsToProcess.length) {
                            // All records processed
                            completeUpdate();
                            return;
                        }

                        var recordId = recordsToProcess[currentIndex];

                        // Update progress UI
                        var progressPercent = (currentIndex / totalRecords) * 100;
                        $('.progress-bar .progress').css('width', progressPercent + '%');
                        $('.progress-count .current').text(currentIndex + 1);

                        // Process record
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_update_record_release_date',
                                record_id: recordId,
                                nonce: '<?php echo wp_create_nonce('rewindrecords_update_dates'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    if (response.data.updated) {
                                        updatedRecords++;
                                    } else {
                                        unchangedRecords++;
                                    }
                                }

                                // Process next record
                                currentIndex++;
                                processNextRecord();
                            },
                            error: function() {
                                // Process next record even if there's an error
                                currentIndex++;
                                processNextRecord();
                            }
                        });
                    }

                    function completeUpdate() {
                        var $progress = $('#update-dates-progress');
                        var $results = $('#update-dates-results');
                        var $controls = $('#update-dates-controls');

                        // Update progress UI to 100%
                        $('.progress-bar .progress').css('width', '100%');

                        // Hide progress and show results
                        $progress.hide();
                        $results.show();
                        $controls.show();

                        // Update results
                        $('.updated-records-count').text(updatedRecords);
                        $('.unchanged-records-count').text(unchangedRecords);
                        $('.total-records-count').text(totalRecords);

                        // Update summary
                        var summaryText = '<?php _e('Successfully updated release dates for {0} records.', 'rewindrecords'); ?>';
                        summaryText = summaryText.replace('{0}', updatedRecords);
                        $('.update-summary').text(summaryText);

                        // Reset variables for next run
                        recordsToProcess = [];
                        currentIndex = 0;
                        totalRecords = 0;
                        updatedRecords = 0;
                        unchangedRecords = 0;
                    }
                });
            </script>
        </div>
        <?php
    }

    /**
     * AJAX handler for getting all records
     */
    public function ajax_get_all_records() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_update_dates')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Get all record IDs
        $args = array(
            'post_type' => 'record',
            'posts_per_page' => -1,
            'fields' => 'ids',
        );

        $query = new WP_Query($args);

        if ($query->have_posts()) {
            wp_send_json_success($query->posts);
        } else {
            wp_send_json_error(__('No records found', 'rewindrecords'));
        }
    }

    /**
     * AJAX handler for updating a record's release date
     */
    public function ajax_update_record_release_date() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_update_dates')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Get record ID
        $record_id = isset($_POST['record_id']) ? intval($_POST['record_id']) : 0;

        if (!$record_id) {
            wp_send_json_error(__('Invalid record ID', 'rewindrecords'));
        }

        // Get record data
        $post = get_post($record_id);

        if (!$post || $post->post_type !== 'record') {
            wp_send_json_error(__('Record not found', 'rewindrecords'));
        }

        // Get artist and title
        $artist = '';
        $title = $post->post_title;

        // Get artist from taxonomy
        $artists = get_the_terms($record_id, 'record_artist');
        if ($artists && !is_wp_error($artists) && !empty($artists)) {
            $artist = $artists[0]->name;
        }

        // Check if we already have a release date
        $current_release_date = get_post_meta($record_id, '_record_release_date', true);
        $updated = false;

        // Try to get release date from iTunes API
        if ($this->update_release_date_from_itunes($record_id, $artist, $title)) {
            $updated = true;
        }
        // If iTunes fails, try MusicBrainz
        else if ($this->update_release_date_from_musicbrainz($record_id, $artist, $title)) {
            $updated = true;
        }
        // If both fail, ensure we have at least a basic release date
        else {
            // Use the CSV import class to ensure a release date
            require_once get_template_directory() . '/includes/class-records-csv-import.php';
            $csv_import = new Rewindrecords_CSV_Import();
            $csv_import->ensure_release_date($record_id);

            // Check if the release date was updated
            $new_release_date = get_post_meta($record_id, '_record_release_date', true);
            $updated = ($new_release_date !== $current_release_date);
        }

        wp_send_json_success(array(
            'updated' => $updated,
            'record_id' => $record_id,
        ));
    }

    /**
     * Update album info from iTunes API
     *
     * @param int $record_id Record ID
     * @param string $artist Artist name
     * @param string $album Album title
     * @return bool True if updated, false otherwise
     */
    private function update_release_date_from_itunes($record_id, $artist, $album) {
        // Build search term
        $search_term = urlencode($artist . ' ' . $album);

        // Build iTunes API URL
        $api_url = 'https://itunes.apple.com/search?term=' . $search_term . '&media=music&entity=album&limit=1';

        // Set request arguments
        $args = array(
            'timeout' => 15,
        );

        // Make request to iTunes API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data) || empty($data['results']) || count($data['results']) === 0) {
            return false;
        }

        // Get the first result
        $album_data = $data['results'][0];

        // Check if we have a release date
        if (empty($album_data['releaseDate'])) {
            return false;
        }

        // Get current release date
        $current_release_date = get_post_meta($record_id, '_record_release_date', true);

        // Get new release date
        $new_release_date = substr($album_data['releaseDate'], 0, 10); // Get YYYY-MM-DD part

        // Update release date if different
        $updated = false;
        if ($new_release_date !== $current_release_date) {
            update_post_meta($record_id, '_record_release_date', $new_release_date);
            $updated = true;
        }

        // Try to get tracklist and additional info
        if (!empty($album_data['collectionId'])) {
            $this->get_album_details_from_itunes($record_id, $album_data['collectionId']);
        }

        return $updated;
    }

    /**
     * Get album details from iTunes API including tracklist
     *
     * @param int $post_id Post ID
     * @param string $collection_id iTunes collection ID
     * @return bool True on success, false on failure
     */
    private function get_album_details_from_itunes($post_id, $collection_id) {
        // Build iTunes API URL for tracks
        $api_url = 'https://itunes.apple.com/lookup?id=' . $collection_id . '&entity=song';

        // Set request arguments
        $args = array(
            'timeout' => 15,
        );

        // Make request to iTunes API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data) || empty($data['results']) || count($data['results']) < 2) {
            return false;
        }

        // The first result is the album, the rest are tracks
        $album_data = $data['results'][0];
        $tracks = array_slice($data['results'], 1);

        // Process tracks
        $tracklist = array();
        foreach ($tracks as $track) {
            if (isset($track['trackName'])) {
                // Format duration from milliseconds to MM:SS
                $duration = '';
                if (isset($track['trackTimeMillis'])) {
                    $seconds = floor($track['trackTimeMillis'] / 1000);
                    $minutes = floor($seconds / 60);
                    $seconds = $seconds % 60;
                    $duration = sprintf('%d:%02d', $minutes, $seconds);
                }

                $track_data = array(
                    'position' => isset($track['trackNumber']) ? $track['trackNumber'] : count($tracklist) + 1,
                    'title' => $track['trackName'],
                    'duration' => $duration,
                );
                $tracklist[] = $track_data;
            }
        }

        // Save tracklist if we have tracks and there isn't one already
        $current_tracklist = get_post_meta($post_id, '_record_tracklist', true);
        if (!empty($tracklist) && empty($current_tracklist)) {
            update_post_meta($post_id, '_record_tracklist', $tracklist);
        }

        // Save additional metadata
        if (!empty($album_data['copyright'])) {
            update_post_meta($post_id, '_record_copyright', $album_data['copyright']);
        }

        // Generate a Dutch description for the album if there isn't content already
        $post = get_post($post_id);
        if ($post && empty($post->post_content)) {
            $this->generate_dutch_description_from_itunes($post_id, $album_data, $tracks);
        }

        return true;
    }

    /**
     * Generate a Dutch description for the album from iTunes data
     *
     * @param int $post_id Post ID
     * @param array $album_data Album data from iTunes
     * @param array $tracks Tracks data from iTunes
     * @return void
     */
    private function generate_dutch_description_from_itunes($post_id, $album_data, $tracks) {
        // Use the helper class to generate the description
        Rewindrecords_Helper::generate_dutch_description_from_itunes($post_id, $album_data, $tracks);
    }



    /**
     * Update release date from MusicBrainz API
     *
     * @param int $record_id Record ID
     * @param string $artist Artist name
     * @param string $album Album title
     * @return bool True if updated, false otherwise
     */
    private function update_release_date_from_musicbrainz($record_id, $artist, $album) {
        // Sanitize search terms
        $search_term = urlencode('artist:' . $artist . ' AND release:' . $album);

        // Build MusicBrainz API URL
        $api_url = 'https://musicbrainz.org/ws/2/release/?query=' . $search_term . '&fmt=json&limit=1';

        // Set user agent as required by MusicBrainz API
        $args = array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'RewindrecordsApp/1.0 ( https://rewindrecords.com )',
            ),
        );

        // Make request to MusicBrainz API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data['releases']) || count($data['releases']) === 0) {
            return false;
        }

        // Get the first release
        $release = $data['releases'][0];

        // Check if we have a release date
        if (empty($release['date'])) {
            return false;
        }

        // Get current release date
        $current_release_date = get_post_meta($record_id, '_record_release_date', true);

        // Get new release date
        $new_release_date = $release['date'];

        // Make sure the release date is in the correct format (YYYY-MM-DD)
        if (preg_match('/^\d{4}$/', $new_release_date)) {
            $new_release_date = $new_release_date . '-01-01';
        } elseif (preg_match('/^\d{4}-\d{2}$/', $new_release_date)) {
            $new_release_date = $new_release_date . '-01';
        }

        // Update release date if different
        if ($new_release_date !== $current_release_date) {
            update_post_meta($record_id, '_record_release_date', $new_release_date);
            return true;
        }

        return false;
    }
}
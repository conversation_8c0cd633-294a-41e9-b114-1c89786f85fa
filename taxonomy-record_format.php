<?php
/**
 * The template for displaying record format archives
 */

get_header();

$term = get_queried_object();
?>

<div class="recordsArchive">
    <div class="contentWrapper">
        <h1 class="bigTitle"><?php echo esc_html($term->name); ?></h1>

        <?php if (!empty($term->description)) : ?>
            <div class="termDescription">
                <?php echo wpautop($term->description); ?>
            </div>
        <?php endif; ?>

        <div class="recordFilters">
            <div class="filterGroup">
                <label for="artist-filter"><?php _e('Artist:', 'rewindrecords'); ?></label>
                <select id="artist-filter">
                    <option value=""><?php _e('All Artists', 'rewindrecords'); ?></option>
                    <?php
                    $artists = get_terms(array(
                        'taxonomy' => 'record_artist',
                        'hide_empty' => true,
                    ));

                    foreach ($artists as $artist) {
                        echo '<option value="' . esc_attr($artist->slug) . '">' . esc_html($artist->name) . '</option>';
                    }
                    ?>
                </select>
            </div>

            <div class="filterGroup">
                <label for="genre-filter"><?php _e('Genre:', 'rewindrecords'); ?></label>
                <select id="genre-filter">
                    <option value=""><?php _e('All Genres', 'rewindrecords'); ?></option>
                    <?php
                    $genres = get_terms(array(
                        'taxonomy' => 'record_genre',
                        'hide_empty' => true,
                    ));

                    foreach ($genres as $genre) {
                        echo '<option value="' . esc_attr($genre->slug) . '">' . esc_html($genre->name) . '</option>';
                    }
                    ?>
                </select>
            </div>
        </div>

        <div class="recordsGrid">
            <?php if (have_posts()) : ?>
                <?php while (have_posts()) : the_post(); ?>
                    <div class="recordItem">
                        <a href="<?php the_permalink(); ?>" class="recordLink">
                            <div class="recordCover">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('record-cover'); ?>
                                <?php else : ?>
                                    <div class="defaultCover">
                                        <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="256" cy="256" r="240" fill="#333" />
                                            <circle cx="256" cy="256" r="120" fill="#F5D042" />
                                            <circle cx="256" cy="256" r="30" fill="#333" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="recordInfo">
                                <h3 class="recordTitle"><?php the_title(); ?></h3>

                                <?php
                                $artists = get_the_terms(get_the_ID(), 'record_artist');
                                if ($artists && !is_wp_error($artists)) :
                                ?>
                                    <div class="recordArtist">
                                        <?php echo esc_html($artists[0]->name); ?>
                                    </div>
                                <?php endif; ?>

                                <?php
                                $year = get_post_meta(get_the_ID(), '_record_year', true);
                                if ($year):
                                ?>
                                    <div class="recordYear"><?php echo esc_html($year); ?></div>
                                <?php endif; ?>

                                <?php
                                $formats = get_the_terms(get_the_ID(), 'record_format');
                                if ($formats && !is_wp_error($formats)) :
                                    $format_names = array();
                                    foreach ($formats as $format) {
                                        $format_names[] = $format->name;
                                    }
                                ?>
                                    <div class="recordFormat"><?php echo esc_html(implode(', ', $format_names)); ?></div>
                                <?php endif; ?>

                                <?php
                                $price = get_post_meta(get_the_ID(), '_record_price', true);
                                if ($price && rewindrecords_show_prices()) :
                                ?>
                                    <div class="recordPrice">€<?php echo number_format($price, 2, ',', '.'); ?></div>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                <?php endwhile; ?>

                <div class="pagination">
                    <?php
                    echo paginate_links(array(
                        'prev_text' => __('&laquo; Previous', 'rewindrecords'),
                        'next_text' => __('Next &raquo;', 'rewindrecords'),
                    ));
                    ?>
                </div>
            <?php else : ?>
                <div class="noRecords">
                    <p><?php _e('No records found.', 'rewindrecords'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php get_footer(); ?>

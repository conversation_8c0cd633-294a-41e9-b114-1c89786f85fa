// out: false
@import 'vw_values.less';
@import 'constants.less';

// Records Archive
.recordsArchive {
  padding: @vw50 0;

  .bigTitle {
    margin-bottom: @vw30;
  }

  .recordFilters {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-end;
    margin: 0 -@vw10;
    margin-bottom: @vw40;
    padding: @vw25;
    background: @almostBlack;
    border-radius: @vw10;
    box-shadow: 0 @vw8 @vw16 rgba(0, 0, 0, 0.2);
    border: 2px solid @hardBlack;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      top: @vw10;
      left: @vw10;
      right: @vw10;
      height: 1px;
      background: linear-gradient(to right, transparent, @secondaryColor, transparent);
      opacity: 0.5;
    }

    &:after {
      content: '';
      position: absolute;
      bottom: @vw10;
      left: @vw10;
      right: @vw10;
      height: 1px;
      background: linear-gradient(to right, transparent, @secondaryColor, transparent);
      opacity: 0.5;
    }

    .filterGroup {
      display: inline-block;
      margin: 0 @vw15 @vw15 @vw15;
      min-width: @vw200;

      label {
        display: block;
        margin-bottom: @vw8;
        color: @hardWhite;
        font-family: @headingFont;
        font-size: @vw18;
        letter-spacing: 1px;
        text-transform: uppercase;
      }

      select {
        display: block;
        width: 100%;
        padding: @vw10 @vw15;
        border: 2px solid @hardWhite;
        border-radius: @vw5;
        background: @hardBlack;
        color: @hardWhite;
        min-width: @vw200;
        font-family: @headingFont;
        font-size: @vw16;
        letter-spacing: 1px;
        text-transform: uppercase;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          border-color: @secondaryColor;
        }

        &:focus {
          border-color: @secondaryColor;
          outline: none;
          box-shadow: 0 0 0 2px rgba(232, 58, 20, 0.3);
        }

        option {
          font-family: @headingFont;
          font-size: @vw16;
          letter-spacing: 1px;
          text-transform: uppercase;
          padding: @vw8;
        }
      }
    }
  }

  .recordsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(@vw250, 1fr));
    gap: @vw30;

    .recordItem {
      transition: transform 0.3s ease;
      &:hover {
        transform: translateY(-@vw5);

        .recordCover img {
          transform: scale(1.05);
        }
      }

      .recordLink {
        display: block;
        text-decoration: none;
        color: @almostWhite;
      }
      .recordCover {
        position: relative;
        width: 100%;
        padding-bottom: 100%; // Square aspect ratio
        margin-bottom: @vw15;
        overflow: hidden;
        background: @vinylColor;
        cursor: pointer;
        .transform(translate3d(0,0,0));
        * {
          cursor: pointer;
        }
        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          .transitionMore(transform, .3s);
        }
        .defaultCover {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: block;
          background: @vinylColor;
          .transform(translate3d(0,0,0));
          svg {
            width: 80%;
            height: 80%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }

      .recordInfo {
        .recordTitle {
          font-size: @vw18;
          font-family: @headingFont;
          font-weight: 400;
          letter-spacing: 1px;
          margin: 0 0 @vw5;
          color: @almostWhite;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .recordArtist {
          font-size: @vw16;
          font-family: @bodyFont;
          color: @secondaryColor;
          margin-bottom: @vw5;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .recordYear, .recordFormat {
          font-size: @vw14;
          font-family: @bodyFont;
          color: @grey;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .recordPrice {
          font-size: @vw16;
          font-family: @headingFont;
          font-weight: 400;
          letter-spacing: 1px;
          color: @secondaryColor;
          margin-top: @vw5;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .noRecords {
      grid-column: 1 / -1;
      text-align: center;
      padding: @vw50 0;

      p {
        margin-bottom: @vw20;
        color: @almostWhite;
        font-size: @vw18;
      }

      .button {
        display: inline-block;
        padding: @vw10 @vw20;
        background: @secondaryColor;
        color: @hardWhite;
        text-decoration: none;
        border-radius: @vw5;
        font-weight: bold;

        &:hover {
          background: darken(@secondaryColor, 10%);
        }
      }
    }
  }

  .pagination {
    margin-top: @vw40;
    text-align: center;
    display: block;
    width: 100%;
    .page-numbers {
      display: inline-block;
      margin: 0 @vw5;
      border: 1px solid @grey;
      .border-radius(50%,50%,50%,50%);
      color: @almostWhite;
      text-decoration: none;
      font-family: "Bebas Neue", cursive;
      width: @vw50;
      height: @vw50;
      cursor: pointer;
      line-height: @vw48;
      transition: border-color 0.3s ease, color 0.3s ease;
      -webkit-transition: border-color 0.3s ease, color 0.3s ease;
      &.current {
        background: @primaryColor;
        border-color: @primaryColor;
        color: @hardWhite;
      }
      &.prev, &.next {
        width: auto;
        .border-radius(@vw50,@vw50,@vw50,@vw50);
        padding: @vw5 @vw15;
        line-height: auto;
        height: auto;
      }
      &:hover:not(.current) {
        border-color: @primaryColor;
        color: @primaryColor;
      }
    }
  }

  .termDescription {
    margin-bottom: @vw30;
    color: @almostWhite;
    font-size: @vw16;
    line-height: 1.5;
  }
}

// Single Record
.singleRecord {
  padding: @vw50 0;

  .recordHeader {
    margin-bottom: @vw40;

    .bigTitle {
      margin-bottom: @vw10;
    }

    .recordArtist {
      font-size: @vw24;
      font-family: @bodyFont;
      color: @secondaryColor;

      a {
        color: @secondaryColor;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .recordContent {
    .flexbox();
    margin: 0 -@vw20;

    .recordCoverCol {
      .inline-block();
      width: 33.333%;
      padding: 0 @vw20;
      min-width: @vw300;

      .recordCover {
        width: 100%;
        max-width: @vw100 * 4;
        margin-bottom: @vw30;
        border-radius: @vw10;
        overflow: hidden;
        box-shadow: 0 @vw10 @vw30 rgba(0, 0, 0, 0.4);

        img {
          width: 100%;
          height: auto;
          display: block;
        }

        .defaultCover {
          width: 100%;
          padding-bottom: 100%;
          position: relative;
          background: @vinylColor;

          svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }
        }
      }

      .recordMeta {
        background: @almostBlack;
        padding: @vw20;
        border-radius: @vw10;

        .metaItem {
          margin-bottom: @vw15;

          &:last-child {
            margin-bottom: 0;
          }

          .metaLabel {
            display: inline-block;
            width: @vw100;
            font-family: @headingFont;
            font-weight: 400;
            letter-spacing: 1px;
            color: @almostWhite;
          }

          .metaValue {
            color: @grey;
            font-family: @bodyFont;

            a {
              color: @secondaryColor;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }

            &.suggestedPrice {
              color: @secondaryColor;
              font-family: @headingFont;
              font-weight: 400;
              letter-spacing: 1px;
              font-size: @vw18;
            }
          }
        }
      }
    }

    .recordInfoCol {
      .inline-block();
      width: 66.666%;
      padding: 0 @vw20;
      min-width: @vw300;

      h2 {
        font-size: @vw24;
        font-family: @headingFont;
        font-weight: 400;
        letter-spacing: 1px;
        color: @almostWhite;
        margin-bottom: @vw20;
        padding-bottom: @vw10;
        border-bottom: 1px solid @grey;
      }

      .recordTracklist {
        margin-bottom: @vw40;

        .tracklistTable {
          width: 100%;
          border-collapse: collapse;

          th, td {
            padding: @vw10;
            text-align: left;
            border-bottom: 1px solid @vinylColor;
          }

          th {
            color: @almostWhite;
            font-family: @headingFont;
            font-weight: 400;
            letter-spacing: 1px;
          }

          td {
            color: @grey;
            font-family: @bodyFont;
          }

          .trackPosition {
            width: @vw80;
          }

          .trackDuration {
            width: @vw100;
            text-align: right;
          }

          tr:hover td {
            background: @almostBlack;
            color: @almostWhite;
          }
        }
      }

      .recordDescription {
        color: @grey;
        font-family: @bodyFont;
        line-height: 1.6;

        p {
          font-family: @bodyFont;
          margin-bottom: @vw15;
        }
      }
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .recordsArchive {
    padding: @vw50-1160 0;

    .bigTitle {
      margin-bottom: @vw30-1160;
    }

    .recordFilters {
      margin: 0 -@vw10-1160;
      margin-bottom: @vw40-1160;
      padding: @vw25-1160;
      border-radius: @vw10-1160;
      box-shadow: 0 @vw8-1160 @vw16-1160 rgba(0, 0, 0, 0.2);
      border-width: 2px;

      &:before, &:after {
        top: @vw10-1160;
        left: @vw10-1160;
        right: @vw10-1160;
        height: 1px;
      }

      &:after {
        bottom: @vw10-1160;
      }

      .filterGroup {
        margin: 0 @vw15-1160 @vw15-1160 @vw15-1160;
        min-width: @vw180-1160;

        label {
          margin-bottom: @vw8-1160;
          font-size: @vw18-1160;
        }

        select {
          padding: @vw10-1160 @vw15-1160;
          border-radius: @vw5-1160;
          min-width: @vw180-1160;
          font-size: @vw16-1160;
          border-width: 2px;

          &:focus {
            box-shadow: 0 0 0 2px rgba(232, 58, 20, 0.3);
          }

          option {
            font-size: @vw16-1160;
            padding: @vw8-1160;
          }
        }
      }
    }

    .recordsGrid {
      margin: 0 -@vw10-1160;

      .recordItem {
        width: 25%;
        padding: 0 @vw10-1160;
        margin-bottom: @vw20-1160;

        &:hover {
          transform: translateY(-@vw5-1160);
        }

        .recordCover {
          margin-bottom: @vw15-1160;
          border-radius: @vw10-1160;
          box-shadow: 0 @vw5-1160 @vw15-1160 rgba(0, 0, 0, 0.3);
        }

        .recordInfo {
          padding: @vw10-1160;

          .recordTitle {
            font-size: @vw18-1160;
            margin: 0 0 @vw5-1160;
          }

          .recordArtist {
            font-size: @vw16-1160;
            margin-bottom: @vw5-1160;
          }

          .recordYear, .recordFormat {
            font-size: @vw14-1160;
          }
        }
      }

      .noRecords {
        padding: @vw50-1160 0;

        p {
          margin-bottom: @vw20-1160;
          font-size: @vw18-1160;
        }

        .button {
          padding: @vw10-1160 @vw20-1160;
          border-radius: @vw5-1160;
        }
      }
    }

    .pagination {
      margin-top: @vw40-1160;

      .page-numbers {
        padding: @vw8-1160 @vw12-1160;
        margin: 0 @vw5-1160;
        border-radius: @vw5-1160;
      }
    }
  }

  .singleRecord {
    padding: @vw50-1160 0;

    .recordHeader {
      margin-bottom: @vw40-1160;

      .bigTitle {
        margin-bottom: @vw10-1160;
      }

      .recordArtist {
        font-size: @vw24-1160;
      }
    }

    .recordContent {
      gap: @vw40-1160;

      .recordCoverCol {
        min-width: @vw300-1160;

        .recordCover {
          max-width: @vw400-1160;
          margin-bottom: @vw30-1160;
          border-radius: @vw10-1160;
          box-shadow: 0 @vw10-1160 @vw30-1160 rgba(0, 0, 0, 0.4);
        }

        .recordMeta {
          padding: @vw20-1160;
          border-radius: @vw10-1160;

          .metaItem {
            margin-bottom: @vw15-1160;

            .metaLabel {
              width: @vw100-1160;
            }
          }
        }
      }

      .recordInfoCol {
        min-width: @vw300-1160;

        h2 {
          font-size: @vw24-1160;
          margin-bottom: @vw20-1160;
          padding-bottom: @vw10-1160;
        }

        .recordTracklist {
          margin-bottom: @vw40-1160;

          .tracklistTable {
            th, td {
              padding: @vw10-1160;
            }

            .trackPosition {
              width: @vw80-1160;
            }

            .trackDuration {
              width: @vw100-1160;
            }
          }
        }

        .recordDescription {
          p {
            margin-bottom: @vw15-1160;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .recordsArchive {
    padding: @vw50-580 0;

    .bigTitle {
      margin-bottom: @vw30-580;
    }

    .recordFilters {
      margin: 0;
      margin-bottom: @vw30-580;
      padding: @vw20-580;
      border-radius: @vw10-580;
      box-shadow: 0 @vw8-580 @vw16-580 rgba(0, 0, 0, 0.2);
      border-width: 2px;

      &:before, &:after {
        top: @vw8-580;
        left: @vw8-580;
        right: @vw8-580;
        height: 1px;
      }

      &:after {
        bottom: @vw8-580;
      }

      .filterGroup {
        display: block;
        width: 100%;
        margin: 0 0 @vw20-580 0;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          display: block;
          margin: 0 0 @vw8-580 0;
          font-size: @vw18-580;
          text-align: center;
        }

        select {
          width: 100%;
          padding: @vw12-580 @vw15-580;
          border-radius: @vw5-580;
          font-size: @vw16-580;
          border-width: 2px;
          text-align-last: center;

          &:focus {
            box-shadow: 0 0 0 2px rgba(232, 58, 20, 0.3);
          }

          option {
            font-size: @vw16-580;
            padding: @vw8-580;
            text-align: left;
          }
        }
      }
    }

    .recordsGrid {
      grid-template-columns: repeat(auto-fill, minmax(@vw150-580, 1fr));
      gap: @vw15-580;

      .recordItem {
        .recordCover {
          margin-bottom: @vw10-580;
          border-radius: @vw8-580;
        }

        .recordInfo {
          padding: @vw8-580;

          .recordTitle {
            font-size: @vw16-580;
            margin: 0 0 @vw3-580;
          }

          .recordArtist {
            font-size: @vw14-580;
            margin-bottom: @vw3-580;
          }

          .recordYear, .recordFormat {
            font-size: @vw12-580;
          }
        }
      }
    }
  }

  .singleRecord {
    padding: @vw30-580 0;

    .recordHeader {
      margin-bottom: @vw30-580;

      .recordArtist {
        font-size: @vw20-580;
      }
    }

    .recordContent {
      margin: 0;

      .recordCoverCol,
      .recordInfoCol {
        display: block;
        width: 100%;
        padding: 0;
        margin-bottom: @vw30-580;
      }

      .recordCoverCol {
        .recordCover {
          max-width: 100%;
        }
      }

      .recordInfoCol {
        h2 {
          font-size: @vw20-580;
        }

        .recordTracklist {
          overflow-x: auto;

          .tracklistTable {
            min-width: 100%;

            th, td {
              padding: @vw8-580;
              font-size: @vw14-580;
            }
          }
        }
      }
    }
  }
}

{"key": "group_record_of_the_week_post_type", "title": "Record of the Week Post Type", "fields": [{"key": "field_rotw_selected_record", "label": "Selected Record", "name": "selected_record", "aria-label": "", "type": "post_object", "instructions": "Select the record for this week", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["record"], "taxonomy": [], "allow_null": 0, "multiple": 0, "return_format": "object", "ui": 1}, {"key": "field_rotw_week_start_date", "label": "Week Start Date", "name": "week_start_date", "aria-label": "", "type": "date_picker", "instructions": "Select the start date for this record of the week (usually Monday)", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "display_format": "d/m/Y", "return_format": "Y-m-d", "first_day": 1}, {"key": "field_rotw_week_end_date", "label": "Week End Date", "name": "week_end_date", "aria-label": "", "type": "date_picker", "instructions": "Select the end date for this record of the week (usually Sunday)", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "display_format": "d/m/Y", "return_format": "Y-m-d", "first_day": 1}, {"key": "field_rotw_description", "label": "Description", "name": "description", "aria-label": "", "type": "wysiwyg", "instructions": "Write a description about why this is the record of the week", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "basic", "media_upload": 0, "delay": 0}, {"key": "field_rotw_is_active", "label": "Is Active", "name": "is_active", "aria-label": "", "type": "true_false", "instructions": "Mark this as the current active record of the week. Only one should be active at a time.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "This is the current Record of the Week", "default_value": 0, "ui": 1, "ui_on_text": "Active", "ui_off_text": "Inactive"}], "location": [[{"param": "post_type", "operator": "==", "value": "record_of_the_week"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1747400803}
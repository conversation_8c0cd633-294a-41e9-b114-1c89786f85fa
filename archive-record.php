<?php
/**
 * The template for displaying record archives
 */

get_header();
?>

<section class="recordsArchive">
    <div class="contentWrapper">
        <h1 class="bigTitle white"><?php _e('Record Collection', 'rewindrecords'); ?></h1>

        <div class="recordFilters">
            <div class="filterGroup">
                <label for="genre-filter">Genre:</label>
                <select id="genre-filter" class="recordFilter">
                    <option value="all">Alle genres</option>
                    <?php
                    $genres = get_terms(array(
                        'taxonomy' => 'record_genre',
                        'hide_empty' => true,
                    ));

                    foreach ($genres as $genre) {
                        echo '<option value="' . esc_attr($genre->slug) . '">' . esc_html($genre->name) . '</option>';
                    }
                    ?>
                </select>
            </div>

            <div class="filterGroup">
                <label for="artist-filter">Artiest:</label>
                <select id="artist-filter" class="recordFilter">
                    <option value="all">Alle artiesten</option>
                    <?php
                    $artists = get_terms(array(
                        'taxonomy' => 'record_artist',
                        'hide_empty' => true,
                    ));

                    foreach ($artists as $artist) {
                        echo '<option value="' . esc_attr($artist->slug) . '">' . esc_html($artist->name) . '</option>';
                    }
                    ?>
                </select>
            </div>

            <div class="filterGroup">
                <label for="sort-filter">Sorteren op:</label>
                <select id="sort-filter" class="recordFilter">
                    <option value="date_desc" selected>Nieuwste eerst</option>
                    <option value="date_asc">Oudste eerst</option>
                    <option value="title_asc">Titel (A-Z)</option>
                    <option value="title_desc">Titel (Z-A)</option>
                    <option value="artist_asc">Artiest (A-Z)</option>
                    <option value="artist_desc">Artiest (Z-A)</option>
                </select>
            </div>
        </div>

        <div class="recordsGrid">
            <?php if (have_posts()) : ?>
                <?php while (have_posts()) : the_post(); ?>
                    <div class="recordItem">
                        <a href="<?php the_permalink(); ?>" class="recordLink">
                            <div class="recordCover">
                                <?php if (has_post_thumbnail()) : ?>
                                    <img class="lazy" data-src="<?php the_post_thumbnail_url('medium'); ?>" alt="<?php the_title(); ?>">
                                <?php else : ?>
                                    <div class="defaultCover">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100">
                                            <circle cx="50" cy="50" r="45" fill="#1A1A1A" />
                                            <circle cx="50" cy="50" r="42" fill="#333333" />
                                            <circle cx="50" cy="50" r="18" fill="#F5D042" />
                                            <circle cx="50" cy="50" r="3" fill="#1A1A1A" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="recordInfo">
                                <h2 class="smallTitle"><?php the_title(); ?></h2>
                                <?php
                                $price = get_post_meta(get_the_ID(), '_record_price', true);
                                if ($price && rewindrecords_show_prices()) :
                                ?>
                                    <div class="smallTitle primary">€<?php echo number_format($price, 2, ',', '.'); ?></div>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                <?php endwhile; ?>


            <?php else : ?>
                <div class="noRecords">
                    <p><?php _e('No records found. Import your collection first.', 'rewindrecords'); ?></p>
                    <?php if (current_user_can('manage_options')) : ?>
                        <a href="<?php echo admin_url('admin.php?page=rewindrecords-import'); ?>" class="button"><?php _e('Import Collection', 'rewindrecords'); ?></a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="pagination">
            <?php
            // Get current URL parameters
            $current_url = get_post_type_archive_link('record');
            $params = array();

            if (isset($_GET['record_genre'])) {
                $params['record_genre'] = sanitize_text_field($_GET['record_genre']);
            }

            if (isset($_GET['record_artist'])) {
                $params['record_artist'] = sanitize_text_field($_GET['record_artist']);
            }

            if (isset($_GET['sort'])) {
                $params['sort'] = sanitize_text_field($_GET['sort']);
            }

            // Build base URL with parameters
            if (!empty($params)) {
                $current_url = add_query_arg($params, $current_url);
            }

            // Check if URL already has parameters
            $format = (strpos($current_url, '?') !== false) ? '&paged=%#%' : '?paged=%#%';

            echo paginate_links(array(
                'base' => $current_url . $format,
                'format' => '',
                'current' => max(1, get_query_var('paged')),
                'total' => $wp_query->max_num_pages,
                'prev_text' => '<i class="icon-arrow-left"></i> ' . __('Previous', 'rewindrecords'),
                'next_text' => __('Next', 'rewindrecords') . ' <i class="icon-arrow-right"></i>',
                'add_args' => false, // Don't add query args, we've already included them in the base
            ));
            ?>
        </div>
    </div>
</section>



<?php
get_footer();

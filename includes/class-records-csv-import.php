<?php
/**
 * Records CSV Import
 *
 * Handles importing records from a CSV file
 */

class Rewindrecords_CSV_Import {

    /**
     * Constructor
     */
    public function __construct() {
        // Add AJAX handlers
        add_action('wp_ajax_rewindrecords_import_csv', array($this, 'ajax_import_csv'));
        add_action('wp_ajax_rewindrecords_parse_csv', array($this, 'ajax_parse_csv'));
        add_action('wp_ajax_rewindrecords_import_batch', array($this, 'ajax_import_batch'));
        add_action('wp_ajax_rewindrecords_cancel_import', array($this, 'ajax_cancel_import'));
        add_action('wp_ajax_rewindrecords_test_api', array($this, 'ajax_test_api'));
    }

    /**
     * Parse CSV file (Kassadump format)
     *
     * @param string $file_path Path to the CSV file
     * @return array|WP_Error Parsed data or error
     */
    public function parse_csv($file_path) {
        $debug_msg = date('Y-m-d H:i:s') . " - Starting parse_csv for file: " . $file_path . "\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        if (!file_exists($file_path)) {
            $error = "CSV file not found: " . $file_path;
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', date('Y-m-d H:i:s') . " - ERROR: " . $error . "\n", FILE_APPEND);
            return new WP_Error('file_not_found', __('CSV file not found', 'rewindrecords'));
        }

        // Real CSV parsing with Discogs + iTunes
        $records = array();
        $not_found_records = array();
        $row = 0;

        if (($handle = fopen($file_path, "r")) !== FALSE) {
            $debug_msg = date('Y-m-d H:i:s') . " - File opened successfully, starting CSV parsing\n";
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

            // Read CSV file - using comma as delimiter for kassadump
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $row++;

                // Skip header row
                if ($row === 1) {
                    $debug_msg = date('Y-m-d H:i:s') . " - Skipping header row\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                    continue;
                }

                // Check if we have enough columns (kassadump has many columns)
                if (count($data) < 11) {
                    continue;
                }

                // Clean up data - remove quotes and extra spaces
                foreach ($data as $key => $value) {
                    $data[$key] = trim($value);
                    if ($data[$key] === '#N/B' || $data[$key] === '') {
                        $data[$key] = '';
                    }
                }

                // Extract data from kassadump columns
                $article_id = isset($data[0]) ? trim($data[0]) : '';
                $barcode = isset($data[1]) ? trim($data[1]) : '';
                $artikel_groep = isset($data[3]) ? trim($data[3]) : '';
                $omschrijving = isset($data[5]) ? trim($data[5]) : '';
                $verkoopprijs_incl = isset($data[8]) ? $this->parse_price($data[8]) : 0;
                $stock = isset($data[10]) ? intval($data[10]) : 0;

                // Skip if no article ID or barcode
                if (empty($article_id) || empty($barcode)) {
                    $debug_msg = date('Y-m-d H:i:s') . " - Row {$row} skipped: missing article_id or barcode\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                    continue;
                }

                // Skip if no description
                if (empty($omschrijving)) {
                    $debug_msg = date('Y-m-d H:i:s') . " - Row {$row} skipped: missing description\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                    continue;
                }

                // PROCESS ALL RECORDS - Don't filter on vinyl only
                $debug_msg = date('Y-m-d H:i:s') . " - Processing row {$row}: {$article_id} - {$barcode} - Group: {$artikel_groep}\n";
                file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

                // ALL records get processed - API lookup will be done during batch processing
                $artist = 'Unknown Artist';  // Placeholder
                $title = 'Unknown Title';    // Placeholder

                $album_data = array(
                    'artist' => $artist,
                    'title' => $title,
                    'source' => 'placeholder',
                    'needs_api_lookup' => true,
                    'barcode' => $barcode
                );

                $debug_msg = date('Y-m-d H:i:s') . " - Record queued for API lookup: {$barcode}\n";
                file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

                // Create record array with kassadump fields
                $record = array(
                    'article_id' => $article_id,
                    'barcode' => $barcode,
                    'description' => $omschrijving,
                    'artist' => $artist,
                    'title' => $title,
                    'category' => $artikel_groep,
                    'price_incl' => $verkoopprijs_incl,
                    'stock' => $stock,
                    'album_data' => $album_data,
                );

                $records[] = $record;
            }

            fclose($handle);
        } else {
            $error = "Could not open file: " . $file_path;
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', date('Y-m-d H:i:s') . " - ERROR: " . $error . "\n", FILE_APPEND);
            return new WP_Error('file_open_error', __('Could not open CSV file', 'rewindrecords'));
        }

        $debug_msg = date('Y-m-d H:i:s') . " - Parse completed - " . count($records) . " records found, " . count($not_found_records) . " not found\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        // Return both found records and not found records
        return array(
            'records' => $records,
            'not_found' => $not_found_records
        );

        /* ORIGINAL CODE COMMENTED OUT FOR TESTING
        $row = 0;

        if (($handle = fopen($file_path, "r")) !== FALSE) {
            $debug_msg = date('Y-m-d H:i:s') . " - File opened successfully\n";
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
            /* ORIGINAL CSV PARSING CODE - COMMENTED OUT FOR TESTING
            // Read CSV file - using comma as delimiter for kassadump
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $row++;

                // Skip header row
                if ($row === 1) {
                    $debug_msg = date('Y-m-d H:i:s') . " - Header row: " . implode(', ', array_slice($data, 0, 10)) . "\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                    continue;
                }

                // Check if we have enough columns (kassadump has many columns)
                if (count($data) < 11) { // We need at least 11 columns for basic data
                    $debug_msg = date('Y-m-d H:i:s') . " - Row $row has only " . count($data) . " columns, skipping\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                    continue;
                }

                if ($row <= 5) { // Log first few rows for debugging
                    $debug_msg = date('Y-m-d H:i:s') . " - Row $row data: " . implode(' | ', array_slice($data, 0, 11)) . "\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                }

                // ALL ORIGINAL CSV PARSING CODE COMMENTED OUT FOR TESTING
                /*
                // Clean up data - remove quotes and extra spaces
                foreach ($data as $key => $value) {
                    $data[$key] = trim($value);
                    // Replace #N/B values with empty strings
                    if ($data[$key] === '#N/B' || $data[$key] === '') {
                        $data[$key] = '';
                    }
                }

                // Extract data from kassadump columns
                $article_id = isset($data[0]) ? trim($data[0]) : '';
                $barcode = isset($data[1]) ? trim($data[1]) : '';
                $artikel_groep = isset($data[3]) ? trim($data[3]) : '';
                $omschrijving = isset($data[5]) ? trim($data[5]) : '';
                $verkoopprijs_incl = isset($data[8]) ? $this->parse_price($data[8]) : 0;
                $stock = isset($data[10]) ? intval($data[10]) : 0;

                if ($row <= 5) { // Debug first few rows
                    $debug_msg = date('Y-m-d H:i:s') . " - Row $row extracted - ID: $article_id, Barcode: $barcode, Group: $artikel_groep, Description: " . substr($omschrijving, 0, 50) . "\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                }

                // Skip if no article ID or barcode
                if (empty($article_id) || empty($barcode)) {
                    if ($row <= 5) {
                        $debug_msg = date('Y-m-d H:i:s') . " - Row $row skipped - missing article_id or barcode\n";
                        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                    }
                    continue;
                }

                // Skip if no description
                if (empty($omschrijving)) {
                    if ($row <= 5) {
                        $debug_msg = date('Y-m-d H:i:s') . " - Row $row skipped - missing description\n";
                        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                    }
                    continue;
                }

                // Only process vinyl records (skip other products)
                if (stripos($artikel_groep, 'vinyl') === false) {
                    if ($row <= 5) {
                        $debug_msg = date('Y-m-d H:i:s') . " - Row $row skipped - not vinyl (group: $artikel_groep)\n";
                        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                    }
                    continue;
                }

                // TEMPORARY: Skip API calls for testing - use simple parsing
                $artist = '';
                $title = '';

                // Simple test parsing for debugging
                if (preg_match('/^([A-Z\s&.]+?)\s+([A-Z\s\(\)&.-]{3,})$/i', $omschrijving, $matches)) {
                    $artist = trim($matches[1]);
                    $title = trim($matches[2]);

                    // Clean up common format issues
                    $artist = str_replace('.', '', $artist);
                    $artist = ucwords(strtolower($artist));
                    $title = ucwords(strtolower($title));

                    // Remove format indicators from title
                    $title = preg_replace('/\s+(1LP|2LP|3LP|LP|CD|12"|7"|180G|VINYL|GATEFOLD|LIMITED|LTD|POSTER\(S\)|BOOKLET|COLORED?|PICTURE\s+DISC|REISSUE|REMASTER|DELUXE|SPECIAL\s+EDT?\.?).*$/i', '', $title);

                    $album_data = array(
                        'artist' => $artist,
                        'title' => $title,
                        'source' => 'test_parsing'
                    );

                    $debug_msg = date('Y-m-d H:i:s') . " - Test parsing for barcode {$barcode} - {$artist} - {$title}\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                } else {
                    // Add to not found list
                    $not_found_records[] = array(
                        'article_id' => $article_id,
                        'barcode' => $barcode,
                        'description' => $omschrijving,
                        'row_number' => $row + 1
                    );
                    $debug_msg = date('Y-m-d H:i:s') . " - Test parsing failed for barcode {$barcode}, description: {$omschrijving}\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                    continue;
                }

                // Create record array with kassadump fields
                $record = array(
                    'article_id' => $article_id,
                    'barcode' => $barcode,
                    'description' => $omschrijving,
                    'artist' => $artist,
                    'title' => $title,
                    'category' => $artikel_groep,
                    'price_incl' => $verkoopprijs_incl,
                    'stock' => $stock,
                    'album_data' => $album_data, // Store for later use
                );

                $records[] = $record;
                $row++;
            }
            fclose($handle);
        } else {
            $error = "Could not open file: " . $file_path;
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', date('Y-m-d H:i:s') . " - ERROR: " . $error . "\n", FILE_APPEND);
            return new WP_Error('file_open_error', __('Could not open CSV file', 'rewindrecords'));
        }

        fclose($handle);

        $debug_msg = date('Y-m-d H:i:s') . " - Parse completed - " . count($records) . " records found, " . count($not_found_records) . " not found\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        // Return both found records and not found records
        return array(
            'records' => $records,
            'not_found' => $not_found_records
        );
        */
        // END OF COMMENTED OUT CODE
    }

    /**
     * Parse price from kassadump format
     *
     * @param string $price_string Price string from CSV
     * @return float Price as float
     */
    private function parse_price($price_string) {
        if (empty($price_string)) {
            return 0;
        }

        // Remove quotes and spaces
        $price_string = str_replace(array('"', ' '), '', $price_string);

        // Replace comma with dot for decimal separator
        $price_string = str_replace(',', '.', $price_string);

        return floatval($price_string);
    }

    /**
     * Lookup album by barcode using Discogs for artist/title and iTunes for metadata
     *
     * @param string $barcode Barcode/EAN
     * @return array|false Album data or false if not found
     */
    private function lookup_album_by_barcode($barcode) {
        $debug_msg = date('Y-m-d H:i:s') . " - Starting Discogs+iTunes lookup for barcode: {$barcode}\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        // Step 1: Get artist and title from Discogs
        $discogs_data = $this->lookup_discogs_by_barcode($barcode);

        if ($discogs_data && !empty($discogs_data['artist']) && !empty($discogs_data['title'])) {
            $debug_msg = date('Y-m-d H:i:s') . " - Discogs SUCCESS: {$discogs_data['artist']} - {$discogs_data['title']}\n";
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

            // Step 2: Get additional metadata from iTunes using artist + title
            $itunes_data = $this->lookup_itunes_by_artist_title($discogs_data['artist'], $discogs_data['title']);

            // Step 3: Combine data - Discogs for artist/title, iTunes for metadata
            $combined_data = array(
                'artist' => $discogs_data['artist'],
                'title' => $discogs_data['title'],
                'source' => 'discogs+itunes'
            );

            // Add iTunes metadata if available (NEVER use Discogs for artwork/tracklist/description)
            if ($itunes_data) {
                $combined_data['release_date'] = $itunes_data['release_date'];
                $combined_data['genre'] = $itunes_data['genre'];
                $combined_data['artwork_url'] = $itunes_data['artwork_url'];  // ONLY from iTunes
                $combined_data['description'] = isset($itunes_data['description']) ? $itunes_data['description'] : '';  // ONLY from iTunes
                $combined_data['tracklist'] = isset($itunes_data['tracklist']) ? $itunes_data['tracklist'] : array();  // ONLY from iTunes

                $debug_msg = date('Y-m-d H:i:s') . " - iTunes metadata added: artwork, tracklist, description from iTunes only\n";
                file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
            } else {
                // NO DISCOGS FALLBACK for artwork/tracklist/description - only basic data
                $combined_data['release_date'] = isset($discogs_data['release_date']) ? $discogs_data['release_date'] : '';
                $combined_data['genre'] = isset($discogs_data['genre']) ? $discogs_data['genre'] : '';
                // NO artwork_url, description, or tracklist from Discogs
                $combined_data['artwork_url'] = '';  // Empty if iTunes fails
                $combined_data['description'] = '';   // Empty if iTunes fails
                $combined_data['tracklist'] = array(); // Empty if iTunes fails

                $debug_msg = date('Y-m-d H:i:s') . " - iTunes metadata not found, NO artwork/tracklist/description added\n";
                file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
            }

            return $combined_data;
        } else {
            // Fallback: Try iTunes directly with barcode
            $debug_msg = date('Y-m-d H:i:s') . " - Discogs failed, trying iTunes directly\n";
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

            $itunes_data = $this->lookup_itunes_by_barcode($barcode);
            if ($itunes_data && !empty($itunes_data['artist']) && !empty($itunes_data['title'])) {
                $debug_msg = date('Y-m-d H:i:s') . " - iTunes fallback SUCCESS: {$itunes_data['artist']} - {$itunes_data['title']}\n";
                file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

                $itunes_data['source'] = 'itunes_fallback';
                return $itunes_data;
            }
        }

        $debug_msg = date('Y-m-d H:i:s') . " - Both Discogs and iTunes failed for barcode: {$barcode}\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        return false;
    }

    /**
     * Lookup album in iTunes by barcode
     *
     * @param string $barcode Barcode/EAN
     * @return array|false Album data or false if not found
     */
    private function lookup_itunes_by_barcode($barcode) {
        // Clean barcode - remove any spaces or special characters
        $barcode = preg_replace('/[^0-9]/', '', $barcode);

        // Skip if barcode is too short or too long
        if (strlen($barcode) < 8 || strlen($barcode) > 14) {
            return false;
        }

        $url = 'https://itunes.apple.com/search?term=' . urlencode($barcode) . '&entity=album&limit=1&country=NL';

        $response = wp_remote_get($url, array(
            'timeout' => 8,
            'headers' => array(
                'User-Agent' => 'Rewindrecords/1.0'
            )
        ));

        if (is_wp_error($response)) {
            error_log('iTunes API Error for barcode ' . $barcode . ': ' . $response->get_error_message());
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (empty($data['results'])) {
            // Try without country restriction
            $url = 'https://itunes.apple.com/search?term=' . urlencode($barcode) . '&entity=album&limit=3';
            $response = wp_remote_get($url, array(
                'timeout' => 8,
                'headers' => array(
                    'User-Agent' => 'Rewindrecords/1.0'
                )
            ));

            if (!is_wp_error($response)) {
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);
            }

            // If still no results, try with UPC prefix variations
            if (empty($data['results'])) {
                // Try adding UPC prefix if barcode doesn't start with it
                if (!str_starts_with($barcode, '0')) {
                    $barcode_with_prefix = '0' . $barcode;
                    $url = 'https://itunes.apple.com/search?term=' . urlencode($barcode_with_prefix) . '&entity=album&limit=1';
                    $response = wp_remote_get($url, array(
                        'timeout' => 8,
                        'headers' => array(
                            'User-Agent' => 'Rewindrecords/1.0'
                        )
                    ));

                    if (!is_wp_error($response)) {
                        $body = wp_remote_retrieve_body($response);
                        $data = json_decode($body, true);
                    }
                }

                if (empty($data['results'])) {
                    error_log("iTunes API: No results found for barcode: " . $barcode);
                    return false;
                }
            }
        }

        $album = $data['results'][0];

        return array(
            'artist' => isset($album['artistName']) ? $album['artistName'] : '',
            'title' => isset($album['collectionName']) ? $album['collectionName'] : '',
            'release_date' => isset($album['releaseDate']) ? substr($album['releaseDate'], 0, 10) : '',
            'genre' => isset($album['primaryGenreName']) ? $album['primaryGenreName'] : '',
            'artwork_url' => isset($album['artworkUrl100']) ? str_replace('100x100', '600x600', $album['artworkUrl100']) : '',
            'description' => isset($album['collectionExplicitness']) ? $album['collectionExplicitness'] : '',
            'source' => 'itunes'
        );
    }

    /**
     * Lookup album in iTunes by artist and title
     *
     * @param string $artist Artist name
     * @param string $title Album title
     * @return array|false Album data or false if not found
     */
    private function lookup_itunes_by_artist_title($artist, $title) {
        // Create search term
        $search_term = $artist . ' ' . $title;
        $search_term = urlencode($search_term);

        $url = 'https://itunes.apple.com/search?term=' . $search_term . '&entity=album&limit=5&country=NL';

        $response = wp_remote_get($url, array(
            'timeout' => 8,
            'headers' => array(
                'User-Agent' => 'Rewindrecords/1.0'
            )
        ));

        if (is_wp_error($response)) {
            error_log('iTunes API Error for search "' . $search_term . '": ' . $response->get_error_message());
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (empty($data['results'])) {
            // Try without country restriction
            $url = 'https://itunes.apple.com/search?term=' . $search_term . '&entity=album&limit=5';
            $response = wp_remote_get($url, array(
                'timeout' => 8,
                'headers' => array(
                    'User-Agent' => 'Rewindrecords/1.0'
                )
            ));

            if (!is_wp_error($response)) {
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);
            }

            if (empty($data['results'])) {
                error_log("iTunes API: No results found for artist/title search: " . $artist . ' - ' . $title);
                return false;
            }
        }

        // Find the best match
        $best_match = null;
        $best_score = 0;

        foreach ($data['results'] as $album) {
            $itunes_artist = isset($album['artistName']) ? $album['artistName'] : '';
            $itunes_title = isset($album['collectionName']) ? $album['collectionName'] : '';

            // Calculate similarity score
            $artist_similarity = $this->calculate_similarity($artist, $itunes_artist);
            $title_similarity = $this->calculate_similarity($title, $itunes_title);
            $total_score = ($artist_similarity + $title_similarity) / 2;

            if ($total_score > $best_score && $total_score > 0.7) { // 70% similarity threshold
                $best_score = $total_score;
                $best_match = $album;
            }
        }

        if (!$best_match) {
            return false;
        }

        return array(
            'artist' => isset($best_match['artistName']) ? $best_match['artistName'] : '',
            'title' => isset($best_match['collectionName']) ? $best_match['collectionName'] : '',
            'release_date' => isset($best_match['releaseDate']) ? substr($best_match['releaseDate'], 0, 10) : '',
            'genre' => isset($best_match['primaryGenreName']) ? $best_match['primaryGenreName'] : '',
            'artwork_url' => isset($best_match['artworkUrl100']) ? str_replace('100x100', '600x600', $best_match['artworkUrl100']) : '',
            'description' => isset($best_match['collectionExplicitness']) ? $best_match['collectionExplicitness'] : '',
            'track_count' => isset($best_match['trackCount']) ? $best_match['trackCount'] : 0,
            'source' => 'itunes'
        );
    }

    /**
     * Calculate similarity between two strings
     *
     * @param string $str1 First string
     * @param string $str2 Second string
     * @return float Similarity score between 0 and 1
     */
    private function calculate_similarity($str1, $str2) {
        // Normalize strings
        $str1 = strtolower(trim($str1));
        $str2 = strtolower(trim($str2));

        // Use similar_text function
        similar_text($str1, $str2, $percent);
        return $percent / 100;
    }

    /**
     * Lookup album in Discogs by barcode
     *
     * @param string $barcode Barcode/EAN
     * @return array|false Album data or false if not found
     */
    private function lookup_discogs_by_barcode($barcode) {
        // Clean barcode - remove any spaces or special characters
        $barcode = preg_replace('/[^0-9]/', '', $barcode);

        // Skip if barcode is too short or too long
        if (strlen($barcode) < 8 || strlen($barcode) > 14) {
            return false;
        }

        // Discogs API endpoint for barcode search
        $url = 'https://api.discogs.com/database/search?barcode=' . urlencode($barcode) . '&type=release&per_page=1';

        $debug_msg = date('Y-m-d H:i:s') . " - Discogs API URL: {$url}\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        $response = wp_remote_get($url, array(
            'timeout' => 8,
            'headers' => array(
                'User-Agent' => 'Rewindrecords/1.0 +https://rewindrecords.nl',
                'Accept' => 'application/vnd.discogs.v2.discogs+json',
                'Authorization' => 'Discogs token=jlSLqCySPknTzWsNsSyqJYcLmAlfNCKtSIEpXNzC'
            )
        ));

        if (is_wp_error($response)) {
            $error_msg = 'Discogs API Error for barcode ' . $barcode . ': ' . $response->get_error_message();
            error_log($error_msg);
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', date('Y-m-d H:i:s') . " - " . $error_msg . "\n", FILE_APPEND);
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $response_code = wp_remote_retrieve_response_code($response);

        $debug_msg = date('Y-m-d H:i:s') . " - Discogs API Response Code: {$response_code}\n";
        $debug_msg .= date('Y-m-d H:i:s') . " - Discogs API Response Body: " . substr($body, 0, 500) . "\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        $data = json_decode($body, true);

        if (empty($data['results'])) {
            $error_msg = "Discogs API: No results found for barcode: " . $barcode;
            error_log($error_msg);
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', date('Y-m-d H:i:s') . " - " . $error_msg . "\n", FILE_APPEND);
            return false;
        }

        $release = $data['results'][0];

        // Extract artist and title
        $artist = '';
        $title = isset($release['title']) ? $release['title'] : '';

        // Parse artist from title (Discogs format is often "Artist - Title")
        if (strpos($title, ' - ') !== false) {
            $parts = explode(' - ', $title, 2);
            $artist = trim($parts[0]);
            $title = trim($parts[1]);
        } else {
            // Fallback: use the first artist if available
            if (isset($release['artist']) && !empty($release['artist'])) {
                $artist = $release['artist'];
            } else {
                return false;
            }
        }

        return array(
            'artist' => $artist,
            'title' => $title,
            'release_date' => isset($release['year']) ? $release['year'] . '-01-01' : '',
            'genre' => isset($release['genre']) && is_array($release['genre']) ? implode(', ', $release['genre']) : '',
            // NO artwork_url from Discogs - only iTunes provides artwork
            'source' => 'discogs'
        );
    }

    /**
     * Improved parsing of description to extract artist and title
     * Handles formats like "DE VISSER. EEFJE HEIMWEE 1LP 180G POSTER(S)"
     *
     * @param string $description Description from kassadump
     * @return array Array with artist and title
     */
    private function parse_description_improved($description) {
        $artist = '';
        $title = '';

        // Clean up the description first
        $description = trim($description);

        // Remove common vinyl/format indicators from the end
        $description = preg_replace('/\s+(1LP|2LP|3LP|LP|CD|12"|7"|180G|VINYL|GATEFOLD|LIMITED|LTD|POSTER\(S\)|BOOKLET|COLORED?|PICTURE\s+DISC|REISSUE|REMASTER|DELUXE|SPECIAL\s+EDT?\.?).*$/i', '', $description);

        // Pattern 1: "LASTNAME. FIRSTNAME ALBUM_TITLE" (like "DE VISSER. EEFJE HEIMWEE")
        if (preg_match('/^([A-Z\s]+)\.\s+([A-Z\s]+?)\s+([A-Z\s\(\)&.-]+)$/i', $description, $matches)) {
            $lastname = trim($matches[1]);
            $firstname = trim($matches[2]);
            $album = trim($matches[3]);

            // Format name properly: "Firstname Lastname"
            $artist = ucwords(strtolower($firstname)) . ' ' . ucwords(strtolower($lastname));
            $title = ucwords(strtolower($album));
        }
        // Pattern 2: "ARTIST ALBUM_TITLE" (standard format)
        else if (preg_match('/^([A-Z\s&.]+?)\s+([A-Z\s\(\)&.-]{3,})$/i', $description, $matches)) {
            $artist = trim($matches[1]);
            $title = trim($matches[2]);

            // Clean up artist name if it has lastname, firstname format
            $artist = $this->clean_artist_name($artist);
            $title = ucwords(strtolower($title));
        }
        // Pattern 3: Try to split on common separators
        else if (preg_match('/^(.+?)\s+[-–—]\s+(.+)$/', $description, $matches)) {
            $artist = trim($matches[1]);
            $title = trim($matches[2]);

            $artist = $this->clean_artist_name($artist);
            $title = ucwords(strtolower($title));
        }
        // Pattern 4: Fallback - split roughly in the middle, assuming first part is artist
        else {
            $words = explode(' ', $description);
            $word_count = count($words);

            if ($word_count >= 3) {
                // Take first 1-2 words as artist, rest as title
                $split_point = $word_count <= 4 ? 1 : 2;
                $artist_words = array_slice($words, 0, $split_point);
                $title_words = array_slice($words, $split_point);

                $artist = implode(' ', $artist_words);
                $title = implode(' ', $title_words);

                $artist = $this->clean_artist_name($artist);
                $title = ucwords(strtolower($title));
            } else {
                // Too few words, can't reliably parse
                $artist = 'Unknown Artist';
                $title = $description;
            }
        }

        // Final cleanup
        $artist = trim($artist);
        $title = trim($title);

        // Remove any remaining format indicators
        $title = preg_replace('/\s+(LP|CD|VINYL)$/i', '', $title);

        return array(
            'artist' => $artist,
            'title' => $title
        );
    }

    /**
     * Legacy parse description function (kept for backward compatibility)
     */
    private function parse_description($description) {
        return $this->parse_description_improved($description);
    }

    /**
     * Parse release date from various formats
     *
     * @param string $date_string Date string from CSV
     * @return string Date in YYYY-MM-DD format
     */
    private function parse_release_date($date_string) {
        // Check if it's empty
        if (empty($date_string)) {
            return '';
        }

        // Try to parse Dutch date format (dd-mmm.-yyyy)
        if (preg_match('/^(\d{1,2})-([a-z]{3,4}\.?)-(\d{4})$/i', $date_string, $matches)) {
            $day = $matches[1];
            $month_abbr = $matches[2];
            $year = $matches[3];

            // Convert Dutch month abbreviation to month number
            $month_map = array(
                'jan' => '01',
                'feb' => '02',
                'mrt' => '03',
                'apr' => '04',
                'mei' => '05',
                'jun' => '06',
                'jul' => '07',
                'aug' => '08',
                'sep' => '09',
                'okt' => '10',
                'nov' => '11',
                'dec' => '12'
            );

            $month_key = strtolower(str_replace('.', '', $month_abbr));
            $month = isset($month_map[$month_key]) ? $month_map[$month_key] : '01';

            return $year . '-' . $month . '-' . str_pad($day, 2, '0', STR_PAD_LEFT);
        }

        // Try to parse DD-MM-YYYY format
        if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $date_string, $matches)) {
            return $matches[3] . '-' . str_pad($matches[2], 2, '0', STR_PAD_LEFT) . '-' . str_pad($matches[1], 2, '0', STR_PAD_LEFT);
        }

        // Try to parse MM/DD/YYYY format
        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $date_string, $matches)) {
            return $matches[3] . '-' . str_pad($matches[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        }

        // Try to parse YYYY-MM-DD format (already correct)
        if (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $date_string, $matches)) {
            return $matches[1] . '-' . str_pad($matches[2], 2, '0', STR_PAD_LEFT) . '-' . str_pad($matches[3], 2, '0', STR_PAD_LEFT);
        }

        // Try to parse just a year
        if (preg_match('/^(\d{4})$/', $date_string, $matches)) {
            return $matches[1] . '-01-01';
        }

        // If we can't parse it, return as is (will be handled by ensure_release_date)
        return $date_string;
    }



    /**
     * Clean up artist name
     *
     * @param string $artist Artist name from CSV
     * @return string Cleaned artist name
     */
    private function clean_artist_name($artist) {
        // Check for reversed name with comma (lastname, firstname)
        if (strpos($artist, ',') !== false) {
            $name_parts = explode(',', $artist);
            if (count($name_parts) == 2) {
                $lastname = trim($name_parts[0]);
                $firstname = trim($name_parts[1]);

                // Remove any leading hyphens or other characters
                $firstname = ltrim($firstname, '- ');

                // Recombine in correct order with proper capitalization
                $lastname = ucwords(strtolower($lastname));
                $firstname = ucwords(strtolower($firstname));

                $artist = $firstname . ' ' . $lastname;

                return $artist;
            }
        }

        // If no comma, just clean up the name
        // Remove any extra spaces
        $artist = preg_replace('/\s+/', ' ', $artist);

        // Capitalize words properly if the name is all caps
        if ($artist === strtoupper($artist)) {
            $artist = ucwords(strtolower($artist));
        }

        // Special case for artists with "Mc" or "Mac" prefix
        $artist = preg_replace_callback('/\b(Mc|Mac)([a-z])/', function($matches) {
            return $matches[1] . strtoupper($matches[2]);
        }, $artist);

        // Special case for artists with apostrophes
        $artist = preg_replace_callback('/\'([a-z])/', function($matches) {
            return "'" . strtoupper($matches[1]);
        }, $artist);

        return $artist;
    }

    /**
     * Import records from CSV data
     *
     * @param array $records Array of record data from CSV
     * @return array Import results
     */
    public function import_records($records) {
        $results = array(
            'total_imported' => 0,
            'total_updated' => 0,
            'errors' => array(),
        );

        // Get existing records by article ID
        $existing_records = $this->get_existing_record_ids_by_article_id();

        foreach ($records as $record) {
            // Skip if no article ID
            if (empty($record['article_id'])) {
                $results['errors'][] = sprintf(__('Skipped record: No article ID for %s - %s', 'rewindrecords'),
                    $record['artist'], $record['title']);
                continue;
            }

            // Check if this record already exists
            $is_update = isset($existing_records[$record['article_id']]);
            $post_id = $is_update ? $existing_records[$record['article_id']] : 0;

            // Import or update the record
            $import_result = $this->import_record($record, $post_id);

            if (is_wp_error($import_result)) {
                $results['errors'][] = $import_result->get_error_message();
            } else {
                if ($is_update) {
                    $results['total_updated']++;
                } else {
                    $results['total_imported']++;
                }
            }
        }

        return $results;
    }

    /**
     * Get existing record by article ID
     *
     * @param string $article_id Article ID to search for
     * @return WP_Post|null Post object if found, null otherwise
     */
    private function get_record_by_article_id($article_id) {
        $posts = get_posts(array(
            'post_type' => 'record',
            'meta_key' => '_record_article_id',
            'meta_value' => $article_id,
            'posts_per_page' => 1,
            'post_status' => 'any'
        ));

        return !empty($posts) ? $posts[0] : null;
    }

    /**
     * Import a single record
     *
     * @param array $record Record data from CSV
     * @param int $post_id Post ID if updating existing record
     * @return int|WP_Error Post ID on success, WP_Error on failure
     */
    private function import_record($record, $post_id = 0) {
        // If no post_id provided, check if record exists by article ID
        if (!$post_id) {
            $existing_post = $this->get_record_by_article_id($record['article_id']);
            if ($existing_post) {
                $post_id = $existing_post->ID;
            }
        }
        // Prepare post data
        $post_data = array(
            'post_title' => $record['artist'] . ' - ' . $record['title'],
            'post_content' => !empty($record['description']) ? $record['description'] : '',
            'post_status' => 'publish',
            'post_type' => 'record',
        );

        // Update existing or create new
        if ($post_id) {
            $post_data['ID'] = $post_id;
            $post_id = wp_update_post($post_data);
        } else {
            $post_id = wp_insert_post($post_data);
        }

        if (is_wp_error($post_id)) {
            return $post_id;
        }

        // Save article ID as meta
        update_post_meta($post_id, '_record_article_id', $record['article_id']);

        // Save price (including VAT)
        if (!empty($record['price_incl'])) {
            update_post_meta($post_id, '_record_price', floatval($record['price_incl']));
        }

        // Save stock
        if (isset($record['stock'])) {
            update_post_meta($post_id, '_record_stock', intval($record['stock']));
        }

        // Save category as format
        if (!empty($record['category'])) {
            wp_set_object_terms($post_id, $record['category'], 'record_format');
        }

        // Save artist
        if (!empty($record['artist'])) {
            wp_set_object_terms($post_id, $record['artist'], 'record_artist');
        }

        // Save barcode/EAN
        if (!empty($record['barcode'])) {
            update_post_meta($post_id, '_record_ean_code', $record['barcode']);
        }

        // If we have album data from API lookup, save additional info
        if (!empty($record['album_data'])) {
            $album_data = $record['album_data'];

            // Save release date (VERY IMPORTANT for sorting!)
            if (!empty($album_data['release_date'])) {
                update_post_meta($post_id, '_record_release_date', $album_data['release_date']);

                // Also extract and save the year for additional sorting options
                $year = substr($album_data['release_date'], 0, 4);
                if (is_numeric($year)) {
                    update_post_meta($post_id, '_record_year', intval($year));
                }
            }

            // Save genre
            if (!empty($album_data['genre'])) {
                wp_set_object_terms($post_id, $album_data['genre'], 'record_genre');
            }

            // Save description if available
            if (!empty($album_data['description'])) {
                update_post_meta($post_id, '_record_description', $album_data['description']);
            }

            // Save track count if available
            if (!empty($album_data['track_count'])) {
                update_post_meta($post_id, '_record_track_count', intval($album_data['track_count']));
            }

            // Save data source for reference
            if (!empty($album_data['source'])) {
                update_post_meta($post_id, '_record_data_source', $album_data['source']);
            }

            // Set album cover if available and not already set
            if (!empty($album_data['artwork_url']) && !has_post_thumbnail($post_id)) {
                $this->set_featured_image_from_url($post_id, $album_data['artwork_url']);
            }
        }

        // Determine product type based on description or group
        $product_type = 'album'; // Default to album

        // Check if this is a non-album product (like a game, merchandise, etc.)
        if (!empty($record['category'])) {
            $group = strtolower($record['category']);
            if (strpos($group, 'spel') !== false ||
                strpos($group, 'game') !== false ||
                strpos($group, 'merch') !== false ||
                strpos($group, 'accessoire') !== false) {
                $product_type = 'other';
            }
        }

        // Set product type taxonomy
        wp_set_object_terms($post_id, $product_type, 'record_product_type');

        // Only try to get album info if this is an album
        if ($product_type === 'album') {
            // Try to get album cover and info
            if (!has_post_thumbnail($post_id)) {
                // First try iTunes API
                $itunes_success = $this->set_album_cover_from_itunes($post_id, $record['artist'], $record['title']);

                // If iTunes fails, try MusicBrainz + Cover Art Archive
                if (!$itunes_success) {
                    $this->set_album_info_from_musicbrainz($post_id, $record['artist'], $record['title']);
                }
            }

            // Make sure we have a release date for sorting
            $this->ensure_release_date($post_id);
        }

        return $post_id;
    }

    /**
     * Set album cover and info from iTunes API
     *
     * @param int $post_id Post ID
     * @param string $artist Artist name
     * @param string $album Album title
     * @return bool True on success, false on failure
     */
    private function set_album_cover_from_itunes($post_id, $artist, $album) {
        // Build search term
        $search_term = urlencode($artist . ' ' . $album);

        // Build iTunes API URL
        $api_url = 'https://itunes.apple.com/search?term=' . $search_term . '&media=music&entity=album&limit=1';

        // Set request arguments
        $args = array(
            'timeout' => 15,
        );

        // Make request to iTunes API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data) || empty($data['results']) || count($data['results']) === 0) {
            return false;
        }

        // Get the first result
        $album_data = $data['results'][0];

        // Check if we have artwork
        if (empty($album_data['artworkUrl100'])) {
            return false;
        }

        // Get high resolution artwork URL (replace 100x100 with 600x600)
        $artwork_url = str_replace('100x100', '600x600', $album_data['artworkUrl100']);

        // Set featured image
        $image_set = $this->set_featured_image_from_url($post_id, $artwork_url);

        // If we successfully set the image, also save some additional metadata
        if ($image_set) {
            // Save genre if available
            if (!empty($album_data['primaryGenreName'])) {
                $genre = Rewindrecords_Helper::translate_genre_to_dutch($album_data['primaryGenreName']);
                wp_set_object_terms($post_id, $genre, 'record_genre');
            }

            // Save release date if available (VERY IMPORTANT!)
            if (!empty($album_data['releaseDate'])) {
                $release_date = substr($album_data['releaseDate'], 0, 10); // Get YYYY-MM-DD part
                update_post_meta($post_id, '_record_release_date', $release_date);

                // Also save the year for additional sorting
                $year = substr($release_date, 0, 4);
                if (is_numeric($year)) {
                    update_post_meta($post_id, '_record_year', intval($year));
                }
            }

            // Try to get tracklist and additional info from iTunes
            $this->get_album_details_from_itunes($post_id, $album_data['collectionId']);

            return true;
        }

        return false;
    }

    /**
     * Get album details from iTunes API including tracklist
     *
     * @param int $post_id Post ID
     * @param string $collection_id iTunes collection ID
     * @return bool True on success, false on failure
     */
    private function get_album_details_from_itunes($post_id, $collection_id) {
        // Build iTunes API URL for tracks
        $api_url = 'https://itunes.apple.com/lookup?id=' . $collection_id . '&entity=song';

        // Set request arguments
        $args = array(
            'timeout' => 15,
        );

        // Make request to iTunes API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data) || empty($data['results']) || count($data['results']) < 2) {
            return false;
        }

        // The first result is the album, the rest are tracks
        $album_data = $data['results'][0];
        $tracks = array_slice($data['results'], 1);

        // Process tracks
        $tracklist = array();
        foreach ($tracks as $track) {
            if (isset($track['trackName'])) {
                // Format duration from milliseconds to MM:SS
                $duration = '';
                if (isset($track['trackTimeMillis'])) {
                    $seconds = floor($track['trackTimeMillis'] / 1000);
                    $minutes = floor($seconds / 60);
                    $seconds = $seconds % 60;
                    $duration = sprintf('%d:%02d', $minutes, $seconds);
                }

                $track_data = array(
                    'position' => isset($track['trackNumber']) ? $track['trackNumber'] : count($tracklist) + 1,
                    'title' => $track['trackName'],
                    'duration' => $duration,
                );
                $tracklist[] = $track_data;
            }
        }

        // Save tracklist if we have tracks
        if (!empty($tracklist)) {
            update_post_meta($post_id, '_record_tracklist', $tracklist);
        }

        // Save additional metadata
        if (!empty($album_data['copyright'])) {
            update_post_meta($post_id, '_record_copyright', $album_data['copyright']);
        }

        // Generate a Dutch description for the album
        $this->generate_dutch_description_from_itunes($post_id, $album_data, $tracks);

        return true;
    }

    /**
     * Generate a Dutch description for the album from iTunes data
     *
     * @param int $post_id Post ID
     * @param array $album_data Album data from iTunes
     * @param array $tracks Tracks data from iTunes
     * @return void
     */
    private function generate_dutch_description_from_itunes($post_id, $album_data, $tracks) {
        // Use the helper class to generate the description
        Rewindrecords_Helper::generate_dutch_description_from_itunes($post_id, $album_data, $tracks);
    }



    /**
     * Get album info from MusicBrainz API, set cover image and tracklist
     *
     * @param int $post_id Post ID
     * @param string $artist Artist name
     * @param string $album Album title
     * @return bool True on success, false on failure
     */
    private function set_album_info_from_musicbrainz($post_id, $artist, $album) {
        // Step 1: Search for the release in MusicBrainz
        $mbid = $this->search_musicbrainz_release($artist, $album);

        if (!$mbid) {
            return false;
        }

        // Step 2: Get release details including tracklist
        $release_data = $this->get_musicbrainz_release_details($mbid);

        if (!$release_data) {
            return false;
        }

        // Step 3: Get cover art from Cover Art Archive
        $cover_art_url = $this->get_cover_art_url($mbid);

        if ($cover_art_url) {
            $this->set_featured_image_from_url($post_id, $cover_art_url);
        }

        // Step 4: Save tracklist if available
        if (!empty($release_data['tracklist'])) {
            update_post_meta($post_id, '_record_tracklist', $release_data['tracklist']);
        }

        // Step 5: Save additional metadata
        if (!empty($release_data['genre'])) {
            wp_set_object_terms($post_id, $release_data['genre'], 'record_genre');
        }

        if (!empty($release_data['release_date'])) {
            update_post_meta($post_id, '_record_release_date', $release_data['release_date']);
        }

        // Step 6: Create a description in Dutch
        $description = '';

        if (!empty($release_data['title'])) {
            $description .= '<h4>' . esc_html($release_data['title']) . '</h4>';
        }

        if (!empty($release_data['artist'])) {
            $description .= '<p><strong>' . __('Artiest:', 'rewindrecords') . '</strong> ' . esc_html($release_data['artist']) . '</p>';
        }

        if (!empty($release_data['genre'])) {
            $description .= '<p><strong>' . __('Genre:', 'rewindrecords') . '</strong> ' . esc_html($release_data['genre']) . '</p>';
        }

        if (!empty($release_data['release_date'])) {
            $formatted_date = date_i18n(get_option('date_format'), strtotime($release_data['release_date']));
            $description .= '<p><strong>' . __('Releasedatum:', 'rewindrecords') . '</strong> ' . esc_html($formatted_date) . '</p>';
        }

        if (!empty($release_data['label'])) {
            $description .= '<p><strong>' . __('Label:', 'rewindrecords') . '</strong> ' . esc_html($release_data['label']) . '</p>';
        }

        if (!empty($release_data['country'])) {
            $description .= '<p><strong>' . __('Land:', 'rewindrecords') . '</strong> ' . esc_html($release_data['country']) . '</p>';
        }

        if (!empty($release_data['format'])) {
            $description .= '<p><strong>' . __('Formaat:', 'rewindrecords') . '</strong> ' . esc_html($release_data['format']) . '</p>';
        }

        // Update post content with the description if it's not empty
        if (!empty($description)) {
            wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $description,
            ));
        }

        return true;
    }

    /**
     * Search for a release in MusicBrainz
     *
     * @param string $artist Artist name
     * @param string $album Album title
     * @return string|bool MusicBrainz ID (MBID) or false if not found
     */
    private function search_musicbrainz_release($artist, $album) {
        // Sanitize search terms
        $search_term = urlencode('artist:' . $artist . ' AND release:' . $album);

        // Build MusicBrainz API URL
        $api_url = 'https://musicbrainz.org/ws/2/release/?query=' . $search_term . '&fmt=json&limit=1';

        // Set user agent as required by MusicBrainz API
        $args = array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'RewindrecordsApp/1.0 ( https://rewindrecords.com )',
            ),
        );

        // Make request to MusicBrainz API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data['releases']) || count($data['releases']) === 0) {
            return false;
        }

        // Return the MBID of the first release
        return $data['releases'][0]['id'];
    }

    /**
     * Get release details from MusicBrainz
     *
     * @param string $mbid MusicBrainz ID
     * @return array|bool Release data or false if not found
     */
    private function get_musicbrainz_release_details($mbid) {
        // Build MusicBrainz API URL
        $api_url = 'https://musicbrainz.org/ws/2/release/' . $mbid . '?inc=recordings+artist-credits+labels+genres&fmt=json';

        // Set user agent as required by MusicBrainz API
        $args = array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'RewindrecordsApp/1.0 ( https://rewindrecords.com )',
            ),
        );

        // Make request to MusicBrainz API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data) || !isset($data['id'])) {
            return false;
        }

        // Sleep for 1 second to respect MusicBrainz rate limiting
        sleep(1);

        // Extract and format release data
        $release_data = array(
            'title' => isset($data['title']) ? $data['title'] : '',
            'artist' => '',
            'release_date' => '',
            'genre' => '',
            'label' => '',
            'country' => isset($data['country']) ? $data['country'] : '',
            'format' => '',
            'tracklist' => array(),
        );

        // Get artist name
        if (isset($data['artist-credit']) && is_array($data['artist-credit']) && !empty($data['artist-credit'])) {
            $artists = array();
            foreach ($data['artist-credit'] as $artist) {
                if (isset($artist['name'])) {
                    $artists[] = $artist['name'];
                }
            }
            $release_data['artist'] = implode(', ', $artists);
        }

        // Get release date
        if (isset($data['date'])) {
            $release_data['release_date'] = $data['date'];
        }

        // Get genre
        if (isset($data['genres']) && is_array($data['genres']) && !empty($data['genres'])) {
            $genres = array();
            foreach ($data['genres'] as $genre) {
                if (isset($genre['name'])) {
                    // Translate genre to Dutch if possible
                    $genres[] = $this->translate_genre_to_dutch($genre['name']);
                }
            }
            $release_data['genre'] = implode(', ', $genres);
        }

        // Get label
        if (isset($data['label-info']) && is_array($data['label-info']) && !empty($data['label-info'])) {
            $labels = array();
            foreach ($data['label-info'] as $label_info) {
                if (isset($label_info['label']['name'])) {
                    $labels[] = $label_info['label']['name'];
                }
            }
            $release_data['label'] = implode(', ', $labels);
        }

        // Get format
        if (isset($data['media']) && is_array($data['media']) && !empty($data['media'])) {
            $formats = array();
            foreach ($data['media'] as $media) {
                if (isset($media['format'])) {
                    $formats[] = $media['format'];
                }
            }
            $release_data['format'] = implode(', ', $formats);
        }

        // Get tracklist
        if (isset($data['media']) && is_array($data['media'])) {
            $tracklist = array();
            $position = 1;

            foreach ($data['media'] as $media) {
                if (isset($media['tracks']) && is_array($media['tracks'])) {
                    foreach ($media['tracks'] as $track) {
                        if (isset($track['title'])) {
                            $track_data = array(
                                'position' => $position,
                                'title' => $track['title'],
                                'duration' => isset($track['length']) ? $this->format_duration($track['length']) : '',
                            );
                            $tracklist[] = $track_data;
                            $position++;
                        }
                    }
                }
            }

            $release_data['tracklist'] = $tracklist;
        }

        return $release_data;
    }

    /**
     * Get cover art URL from Cover Art Archive
     *
     * @param string $mbid MusicBrainz ID
     * @return string|bool Cover art URL or false if not found
     */
    private function get_cover_art_url($mbid) {
        // Build Cover Art Archive API URL
        $api_url = 'https://coverartarchive.org/release/' . $mbid;

        // Set user agent
        $args = array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'RewindrecordsApp/1.0 ( https://rewindrecords.com )',
                'Accept' => 'application/json',
            ),
        );

        // Make request to Cover Art Archive API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data) || empty($data['images'])) {
            return false;
        }

        // Look for front cover
        foreach ($data['images'] as $image) {
            if (isset($image['front']) && $image['front'] === true && isset($image['image'])) {
                return $image['image'];
            }
        }

        // If no front cover is marked, use the first image
        if (isset($data['images'][0]['image'])) {
            return $data['images'][0]['image'];
        }

        return false;
    }

    /**
     * Format duration in milliseconds to MM:SS format
     *
     * @param int $milliseconds Duration in milliseconds
     * @return string Formatted duration
     */
    private function format_duration($milliseconds) {
        $seconds = floor($milliseconds / 1000);
        $minutes = floor($seconds / 60);
        $seconds = $seconds % 60;

        return sprintf('%d:%02d', $minutes, $seconds);
    }

    /**
     * Translate common music genres from English to Dutch
     *
     * @param string $genre Genre in English
     * @return string Genre in Dutch
     */
    private function translate_genre_to_dutch($genre) {
        $translations = array(
            'Rock' => 'Rock',
            'Pop' => 'Pop',
            'Alternative' => 'Alternatief',
            'Alternative Rock' => 'Alternatieve Rock',
            'Metal' => 'Metal',
            'Heavy Metal' => 'Heavy Metal',
            'Jazz' => 'Jazz',
            'Blues' => 'Blues',
            'Classical' => 'Klassiek',
            'R&B/Soul' => 'R&B/Soul',
            'Hip-Hop/Rap' => 'Hip-Hop/Rap',
            'Electronic' => 'Elektronisch',
            'Dance' => 'Dance',
            'Reggae' => 'Reggae',
            'Country' => 'Country',
            'Folk' => 'Folk',
            'World' => 'Wereldmuziek',
            'Latin' => 'Latijns',
            'New Age' => 'New Age',
            'Soundtrack' => 'Soundtrack',
            'Vocal' => 'Vocaal',
            'Christian & Gospel' => 'Christelijk & Gospel',
            'Indie Rock' => 'Indie Rock',
            'Pop/Rock' => 'Pop/Rock',
            'Hard Rock' => 'Hard Rock',
            'Punk' => 'Punk',
            'Punk Rock' => 'Punk Rock',
            'Grunge' => 'Grunge',
            'Progressive Rock' => 'Progressieve Rock',
            'Psychedelic Rock' => 'Psychedelische Rock',
            'Classic Rock' => 'Classic Rock',
            'Soul' => 'Soul',
            'Funk' => 'Funk',
            'Disco' => 'Disco',
            'House' => 'House',
            'Techno' => 'Techno',
            'Trance' => 'Trance',
            'Ambient' => 'Ambient',
            'Trip-Hop' => 'Trip-Hop',
            'Singer/Songwriter' => 'Singer/Songwriter',
        );

        // Return the Dutch translation if available, otherwise return the original genre
        return isset($translations[$genre]) ? $translations[$genre] : $genre;
    }

    /**
     * Download an image from a URL and set it as the featured image for a post
     *
     * @param int $post_id Post ID
     * @param string $image_url URL of the image
     * @return bool True on success, false on failure
     */
    private function set_featured_image_from_url($post_id, $image_url) {
        // Get the file name from the URL
        $filename = basename($image_url);

        // Download file to temp location
        $tmp = download_url($image_url);

        // Check for download errors
        if (is_wp_error($tmp)) {
            return false;
        }

        // Prepare file array for media_handle_sideload
        $file_array = array(
            'name' => $filename,
            'tmp_name' => $tmp
        );

        // Do the upload and get attachment ID
        $attachment_id = media_handle_sideload($file_array, $post_id);

        // Check for upload errors
        if (is_wp_error($attachment_id)) {
            // Clean up temp file
            @unlink($tmp);
            return false;
        }

        // Set as featured image
        set_post_thumbnail($post_id, $attachment_id);

        return true;
    }

    /**
     * Ensure a record has a release date for sorting
     *
     * @param int $post_id Post ID
     * @return void
     */
    private function ensure_release_date($post_id) {
        // Check if we already have a release date
        $release_date = get_post_meta($post_id, '_record_release_date', true);

        if (empty($release_date)) {
            // Try to get year from post meta
            $year = get_post_meta($post_id, '_record_year', true);

            if (!empty($year) && is_numeric($year)) {
                // If we only have a year, set the release date to January 1st of that year
                $release_date = $year . '-01-01';
                update_post_meta($post_id, '_record_release_date', $release_date);
            } else {
                // If we don't have a year, use the post date as a fallback
                $post = get_post($post_id);
                if ($post) {
                    $post_date = substr($post->post_date, 0, 10); // Get YYYY-MM-DD part
                    update_post_meta($post_id, '_record_release_date', $post_date);
                }
            }
        }

        // Make sure the release date is in the correct format (YYYY-MM-DD)
        if (!empty($release_date)) {
            // Check if it's just a year
            if (preg_match('/^\d{4}$/', $release_date)) {
                $release_date = $release_date . '-01-01';
                update_post_meta($post_id, '_record_release_date', $release_date);
            }
            // Check if it's in MM/DD/YYYY format
            elseif (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $release_date, $matches)) {
                $release_date = $matches[3] . '-' . str_pad($matches[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($matches[2], 2, '0', STR_PAD_LEFT);
                update_post_meta($post_id, '_record_release_date', $release_date);
            }
            // Check if it's in DD-MM-YYYY format
            elseif (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $release_date, $matches)) {
                $release_date = $matches[3] . '-' . str_pad($matches[2], 2, '0', STR_PAD_LEFT) . '-' . str_pad($matches[1], 2, '0', STR_PAD_LEFT);
                update_post_meta($post_id, '_record_release_date', $release_date);
            }
        }
    }

    /**
     * Get existing records by article ID
     *
     * @return array Array of post IDs keyed by article ID
     */
    private function get_existing_record_ids_by_article_id() {
        $records = array();

        $args = array(
            'post_type' => 'record',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_record_article_id',
                    'compare' => 'EXISTS',
                ),
            ),
        );

        $query = new WP_Query($args);

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $article_id = get_post_meta(get_the_ID(), '_record_article_id', true);
                if (!empty($article_id)) {
                    $records[$article_id] = get_the_ID();
                }
            }
        }

        wp_reset_postdata();

        return $records;
    }

    /**
     * AJAX handler for parsing CSV (first step)
     */
    public function ajax_parse_csv() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_csv_import')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Check if file was uploaded
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            error_log("Rewindrecords CSV Import: File upload error - " . (isset($_FILES['csv_file']['error']) ? $_FILES['csv_file']['error'] : 'no file'));
            wp_send_json_error(__('No file uploaded or upload error', 'rewindrecords'));
        }

        error_log("Rewindrecords CSV Import: File uploaded successfully - " . $_FILES['csv_file']['name'] . " (" . $_FILES['csv_file']['size'] . " bytes)");

        // Write debug info to custom log
        $debug_msg = date('Y-m-d H:i:s') . " - File uploaded: " . $_FILES['csv_file']['name'] . " (" . $_FILES['csv_file']['size'] . " bytes)\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        // Test if we can read the file
        $test_file = $_FILES['csv_file']['tmp_name'];
        if (!file_exists($test_file)) {
            $error = "Temp file does not exist: " . $test_file;
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', date('Y-m-d H:i:s') . " - ERROR: " . $error . "\n", FILE_APPEND);
            wp_send_json_error($error);
        }

        // Try to read first few lines
        $handle = fopen($test_file, 'r');
        if (!$handle) {
            $error = "Cannot open temp file: " . $test_file;
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', date('Y-m-d H:i:s') . " - ERROR: " . $error . "\n", FILE_APPEND);
            wp_send_json_error($error);
        }

        $line_count = 0;
        while (($line = fgets($handle)) !== false && $line_count < 3) {
            $debug_msg = date('Y-m-d H:i:s') . " - Line " . ($line_count + 1) . ": " . substr(trim($line), 0, 100) . "\n";
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
            $line_count++;
        }
        fclose($handle);

        // Stop any existing imports and clean up old batch data
        $this->stop_existing_imports();

        // Parse CSV file
        try {
            $parse_result = $this->parse_csv($_FILES['csv_file']['tmp_name']);

            if (is_wp_error($parse_result)) {
                error_log("Rewindrecords CSV Import: Parse error - " . $parse_result->get_error_message());
                wp_send_json_error($parse_result->get_error_message());
            }
        } catch (Exception $e) {
            $error_msg = "Rewindrecords CSV Import: Exception during parse - " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine();
            error_log($error_msg);
            // Also write to a custom log file
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', date('Y-m-d H:i:s') . " - " . $error_msg . "\n", FILE_APPEND);
            wp_send_json_error('Parse failed: ' . $e->getMessage());
        }

        $records = $parse_result['records'];
        $not_found = $parse_result['not_found'];

        // Store records in transient for batch processing (extend timeout to 3 hours)
        $batch_id = uniqid('csv_import_');
        set_transient('rewindrecords_csv_batch_' . $batch_id, $records, 3 * HOUR_IN_SECONDS);
        set_transient('rewindrecords_csv_not_found_' . $batch_id, $not_found, 3 * HOUR_IN_SECONDS);

        // Mark this batch as the active import
        set_transient('rewindrecords_active_import', $batch_id, 3 * HOUR_IN_SECONDS);

        error_log("Rewindrecords CSV Import: Created batch " . $batch_id . " with " . count($records) . " records and " . count($not_found) . " not found");

        wp_send_json_success(array(
            'batch_id' => $batch_id,
            'total_records' => count($records),
            'not_found_count' => count($not_found),
            'message' => sprintf(__('Found %d records to import (%d not found in API)', 'rewindrecords'), count($records), count($not_found))
        ));
    }

    /**
     * AJAX handler for importing a batch of records
     */
    public function ajax_import_batch() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_csv_import')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        $batch_id = sanitize_text_field($_POST['batch_id']);
        $offset = intval($_POST['offset']);
        $batch_size = 10; // Process 10 records at a time

        // Check if this import is still active (not cancelled by a new import)
        $active_import = get_transient('rewindrecords_active_import');
        if ($active_import !== $batch_id) {
            wp_send_json_error(__('Import was cancelled by a new import', 'rewindrecords'));
        }

        // Get records from transient
        $all_records = get_transient('rewindrecords_csv_batch_' . $batch_id);
        if (!$all_records) {
            error_log("Rewindrecords CSV Import: Batch data not found for ID: " . $batch_id);
            wp_send_json_error(__('Batch data not found or expired', 'rewindrecords'));
        }

        error_log("Rewindrecords CSV Import: Processing batch " . $batch_id . " with " . count($all_records) . " records, offset: " . $offset);

        // Get batch of records to process
        $records_batch = array_slice($all_records, $offset, $batch_size);

        if (empty($records_batch)) {
            // Import completed - get not found records and clean up
            $not_found = get_transient('rewindrecords_csv_not_found_' . $batch_id);

            delete_transient('rewindrecords_csv_batch_' . $batch_id);
            delete_transient('rewindrecords_csv_not_found_' . $batch_id);
            delete_transient('rewindrecords_active_import');

            wp_send_json_success(array(
                'completed' => true,
                'not_found' => $not_found ? $not_found : array(),
                'message' => __('Import completed successfully', 'rewindrecords')
            ));
        }

        // Debug: Check what's in the batch
        $debug_msg = date('Y-m-d H:i:s') . " - Processing batch with " . count($records_batch) . " records\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        // Process API lookups for this batch
        $processed_batch = array();
        foreach ($records_batch as $index => $record) {
            $debug_msg = date('Y-m-d H:i:s') . " - Batch record {$index}: " . $record['article_id'] . " - " . $record['barcode'] . "\n";
            file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

            if (isset($record['album_data']['needs_api_lookup']) && $record['album_data']['needs_api_lookup']) {
                $barcode = $record['barcode'];
                $debug_msg = date('Y-m-d H:i:s') . " - Doing API lookup for {$barcode}\n";
                file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

                // Test with a known barcode first
                if ($barcode === '0602537972050') {
                    $debug_msg = date('Y-m-d H:i:s') . " - Testing with known Portishead barcode\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                }

                // Do API lookup
                $api_data = $this->lookup_album_by_barcode($barcode);

                $debug_msg = date('Y-m-d H:i:s') . " - API lookup returned: " . print_r($api_data, true) . "\n";
                file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

                if ($api_data && !empty($api_data['artist']) && !empty($api_data['title'])) {
                    // SUCCESS: Update with API data
                    $record['artist'] = $api_data['artist'];
                    $record['title'] = $api_data['title'];
                    $record['album_data'] = $api_data;

                    $debug_msg = date('Y-m-d H:i:s') . " - API SUCCESS: {$barcode} -> {$api_data['artist']} - {$api_data['title']}\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
                } else {
                    // FAILED: Keep placeholder but mark as not found
                    $debug_msg = date('Y-m-d H:i:s') . " - API FAILED: {$barcode}, keeping placeholder\n";
                    file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

                    // Remove the API lookup flag
                    unset($record['album_data']['needs_api_lookup']);
                    $record['album_data']['source'] = 'api_failed';
                }
            } else {
                $debug_msg = date('Y-m-d H:i:s') . " - Record {$record['article_id']} does not need API lookup\n";
                file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);
            }
            $processed_batch[] = $record;
        }

        // Import this batch
        $result = $this->import_records($processed_batch);

        wp_send_json_success(array(
            'completed' => false,
            'processed' => count($records_batch),
            'new_records' => $result['total_imported'],
            'updated_records' => $result['total_updated'],
            'errors' => $result['errors'],
            'next_offset' => $offset + $batch_size
        ));
    }

    /**
     * Stop any existing imports and clean up old batch data
     */
    private function stop_existing_imports() {
        // Get the current active import
        $active_import = get_transient('rewindrecords_active_import');

        if ($active_import) {
            // Delete the active import batch data
            delete_transient('rewindrecords_csv_batch_' . $active_import);
            delete_transient('rewindrecords_csv_not_found_' . $active_import);
            delete_transient('rewindrecords_active_import');

            error_log("Rewindrecords CSV Import: Stopped existing import with batch ID: " . $active_import);
        }

        // Clean up any old batch transients (older than 1 hour)
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_rewindrecords_csv_batch_%' AND option_value < " . (time() - HOUR_IN_SECONDS));
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_rewindrecords_csv_batch_%' AND option_value < " . time());
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_rewindrecords_csv_not_found_%' AND option_value < " . (time() - HOUR_IN_SECONDS));
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_rewindrecords_csv_not_found_%' AND option_value < " . time());
    }

    /**
     * AJAX handler for testing API
     */
    public function ajax_test_api() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_csv_import')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        $barcode = sanitize_text_field($_POST['barcode']);

        $debug_msg = date('Y-m-d H:i:s') . " - API Test started for barcode: {$barcode}\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        // Test the full Discogs + iTunes workflow
        $result = $this->lookup_album_by_barcode($barcode);
        $debug_msg = date('Y-m-d H:i:s') . " - Combined API result: " . print_r($result, true) . "\n";
        file_put_contents(WP_CONTENT_DIR . '/rewindrecords-debug.log', $debug_msg, FILE_APPEND);

        if ($result && !empty($result['artist']) && !empty($result['title'])) {
            $message = sprintf(
                'SUCCESS: %s - %s (Source: %s, Genre: %s, Release: %s)',
                $result['artist'],
                $result['title'],
                isset($result['source']) ? $result['source'] : 'Unknown',
                isset($result['genre']) ? $result['genre'] : 'Unknown',
                isset($result['release_date']) ? $result['release_date'] : 'Unknown'
            );

            wp_send_json_success(array('message' => $message));
        } else {
            wp_send_json_error('Both Discogs and iTunes APIs failed for barcode: ' . $barcode);
        }
    }

    /**
     * AJAX handler for cancelling import
     */
    public function ajax_cancel_import() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_csv_import')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        $batch_id = sanitize_text_field($_POST['batch_id']);

        // Delete the batch data and active import marker
        delete_transient('rewindrecords_csv_batch_' . $batch_id);
        delete_transient('rewindrecords_active_import');

        error_log("Rewindrecords CSV Import: Import cancelled by user for batch ID: " . $batch_id);

        wp_send_json_success(array(
            'message' => __('Import cancelled successfully', 'rewindrecords')
        ));
    }

    /**
     * AJAX handler for importing CSV (legacy - for backward compatibility)
     */
    public function ajax_import_csv() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_csv_import')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Check if file was uploaded
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(__('No file uploaded or upload error', 'rewindrecords'));
        }

        // Parse CSV file
        $parse_result = $this->parse_csv($_FILES['csv_file']['tmp_name']);

        if (is_wp_error($parse_result)) {
            wp_send_json_error($parse_result->get_error_message());
        }

        $records = $parse_result['records'];
        $not_found = $parse_result['not_found'];

        // Import records
        $result = $this->import_records($records);

        // Format response
        $message = sprintf(
            __('Successfully imported %d new records and updated %d existing records from CSV.', 'rewindrecords'),
            $result['total_imported'],
            $result['total_updated']
        );

        wp_send_json_success(array(
            'message' => $message,
            'total_imported' => $result['total_imported'],
            'total_updated' => $result['total_updated'],
            'total' => $result['total_imported'] + $result['total_updated'],
            'errors' => $result['errors'],
            'not_found' => $not_found,
        ));
    }
}

<?php
/**
 * The template for displaying single record
 */

get_header();

// Get record metadata
$record_id = get_post_meta(get_the_ID(), '_record_id', true);
$year = get_post_meta(get_the_ID(), '_record_year', true);
$country = get_post_meta(get_the_ID(), '_record_country', true);
$tracklist = get_post_meta(get_the_ID(), '_record_tracklist', true);
$price = get_post_meta(get_the_ID(), '_record_price', true);
$release_date = get_post_meta(get_the_ID(), '_record_release_date', true);
?>

<section class="singleRecord">
    <div class="contentWrapper">
        <div class="recordHeader">
            <h1 class="bigTitle white"><?php the_title(); ?></h1>

            <?php
            $artists = get_the_terms(get_the_ID(), 'record_artist');
            if ($artists && !is_wp_error($artists)) :
                $artist_links = array();
                foreach ($artists as $artist) {
                    $artist_links[] = '<a href="' . esc_url(get_term_link($artist)) . '">' . esc_html($artist->name) . '</a>';
                }
            ?>
                <div class="recordArtist">
                    <?php echo implode(', ', $artist_links); ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="recordContent">
            <div class="recordCoverCol">
                <div class="recordCover">
                    <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('large'); ?>
                    <?php else : ?>
                        <div class="defaultCover">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="45" fill="#1A1A1A" />
                                <circle cx="50" cy="50" r="42" fill="#333333" />
                                <circle cx="50" cy="50" r="18" fill="#F5D042" />
                                <circle cx="50" cy="50" r="3" fill="#1A1A1A" />
                            </svg>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="recordMeta">
                    <?php if ($release_date) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Release Date:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($release_date))); ?></span>
                        </div>
                    <?php elseif ($year) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Year:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo esc_html($year); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if ($country) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Country:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo esc_html($country); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php
                    $formats = get_the_terms(get_the_ID(), 'record_format');
                    if ($formats && !is_wp_error($formats)) :
                        $format_names = array();
                        foreach ($formats as $format) {
                            $format_names[] = $format->name;
                        }
                    ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Format:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo esc_html(implode(', ', $format_names)); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php
                    $genres = get_the_terms(get_the_ID(), 'record_genre');
                    if ($genres && !is_wp_error($genres)) :
                        $genre_links = array();
                        foreach ($genres as $genre) {
                            $genre_links[] = '<a href="' . esc_url(get_term_link($genre)) . '">' . esc_html($genre->name) . '</a>';
                        }
                    ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Genres:', 'rewindrecords'); ?></span>
                            <span class="metaValue"><?php echo implode(', ', $genre_links); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if ($price && rewindrecords_show_prices()) : ?>
                        <div class="metaItem">
                            <span class="metaLabel"><?php _e('Price:', 'rewindrecords'); ?></span>
                            <span class="metaValue price">€<?php echo number_format($price, 2, ',', '.'); ?></span>
                        </div>
                    <?php endif; ?>


                </div>
            </div>

            <div class="recordInfoCol">
                <?php if (!empty($tracklist)) : ?>
                    <div class="recordTracklist">
                        <h2><?php _e('Tracklist', 'rewindrecords'); ?></h2>
                        <table class="tracklistTable">
                            <thead>
                                <tr>
                                    <th class="trackPosition"><?php _e('Nr', 'rewindrecords'); ?></th>
                                    <th class="trackTitle"><?php _e('Title', 'rewindrecords'); ?></th>
                                    <th class="trackDuration"><?php _e('Duration', 'rewindrecords'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $current_disc = '';
                                foreach ($tracklist as $track) :
                                    // Check if this is a multi-disc album and we're starting a new disc
                                    if (isset($track['position']) && strpos($track['position'], '-') !== false) {
                                        $position_parts = explode('-', $track['position']);
                                        if (count($position_parts) > 1) {
                                            $disc = trim($position_parts[0]);
                                            if ($disc !== $current_disc) {
                                                $current_disc = $disc;
                                                echo '<tr class="disc-header"><td colspan="3"><strong>' . sprintf(__('Disc %s', 'rewindrecords'), esc_html($disc)) . '</strong></td></tr>';
                                            }
                                        }
                                    }
                                ?>
                                    <tr>
                                        <td class="trackPosition">
                                            <?php
                                            // If it's a multi-disc album, only show the track number part
                                            if (isset($track['position']) && strpos($track['position'], '-') !== false) {
                                                $position_parts = explode('-', $track['position']);
                                                if (count($position_parts) > 1) {
                                                    echo esc_html(trim($position_parts[1]));
                                                } else {
                                                    echo esc_html($track['position']);
                                                }
                                            } else {
                                                echo esc_html($track['position']);
                                            }
                                            ?>
                                        </td>
                                        <td class="trackTitle"><?php echo esc_html($track['title']); ?></td>
                                        <td class="trackDuration"><?php echo esc_html($track['duration']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <?php if (get_the_content()) : ?>
                    <div class="recordDescription">
                        <h2><?php _e('Over dit album', 'rewindrecords'); ?></h2>
                        <div class="recordContent">
                            <?php the_content(); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
get_footer();

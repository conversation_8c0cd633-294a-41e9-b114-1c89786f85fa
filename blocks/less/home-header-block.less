// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.homeHeaderBlock {
  &.inview {
    .cols {
      .col {
        .imageWrapper {
          .innerImage {
              img, video {
                .transform(scale(1));
              }
            }
          }
        }
    }
  }
  .cols {
    display: block;

    @media (max-width: 768px) {
      display: block;
      text-align: center;
    }

    .titleWrapper {
      position: relative;
      .textLink {
        position: absolute;
        right: @vw100;
        top: @vw70;
        transition-delay: 1.2s;
      }
    }

    .col {
      display: inline-block;
      width: 50%;
      vertical-align: middle;

      @media (max-width: 768px) {
        display: block;
        width: 100%;
        margin-bottom: @vw40;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .imageWrapper {
        width: 100%;
        overflow: hidden;
        .transform(translate3d(0,0,0));
        .innerImage {
          .paddingRatio(1,1);
        }
        img,
        video {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: block;
          object-fit: cover;
          object-position: center;
          .transform(scale(1.2));
          .transitionMore(transform , .9s, .45s, cubic-bezier(0.83, 0, 0.17, 1));
          will-change: transform;
        }
      }
    }

    .text {
      margin: @vw40 0 @vw60 0;
      padding-left: @vw118;
      padding-right: @vw100;
      p {
        font-style: italic;
      }
    }
    .links {
      padding-left: @vw118;
    }
  }

  // Media position variations
  &.media-left {
    .cols {
      direction: ltr;
    }
  }

  &.media-right {
    .cols {
      direction: rtl;

      .col {
        direction: ltr;
      }
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .media-text-block {
    padding: @vw50-1160 0;

    .cols {
      margin: 0 -@vw15-1160;

      .media-column,
      .text-column {
        min-width: @vw250-1160;
      }

      .media-column {
        .imageWrapper {
          box-shadow: 0 @vw10-1160 @vw30-1160 rgba(0, 0, 0, 0.4);
        }
      }

      .text-column {
        .media-text-title {
          font-size: @vw32-1160;
          margin-bottom: @vw20-1160;
        }

        .media-text-content-text {
          font-size: @vw16-1160;
          margin-bottom: @vw30-1160;

          p {
            margin-bottom: @vw15-1160;
          }
        }

        .links {
          margin: 0 -@vw7-1160;

          .media-text-link {
            display: inline-block;
            margin: 0 @vw7-1160 @vw15-1160 @vw7-1160;
            font-size: @vw18-1160;

            .link-text {
              margin-right: @vw10-1160;
            }

            i {
              font-size: @vw16-1160;
            }

            &:hover {
              i {
                transform: translateX(@vw5-1160);
              }
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 768px) {
  .media-text-block {
    &.media-left,
    &.media-right {
      .media-text-content {
        .media-column {
          width: 100%;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .media-text-block {
    padding: @vw50-580 0;

    .media-text-content {
      gap: @vw30-580;

      .media-column,
      .text-column {
        min-width: 100%;
      }

      .media-column {
        .media-image,
        .media-video {
          box-shadow: 0 @vw10-580 @vw30-580 rgba(0, 0, 0, 0.4);
        }
      }

      .text-column {
        .media-text-title {
          font-size: @vw32-580;
          margin-bottom: @vw20-580;
        }

        .media-text-content-text {
          font-size: @vw16-580;
          margin-bottom: @vw30-580;

          p {
            margin-bottom: @vw15-580;
          }
        }

        .links {
          gap: @vw15-580;

          .media-text-link {
            font-size: @vw18-580;

            .link-text {
              margin-right: @vw10-580;
            }

            i {
              font-size: @vw16-580;
            }

            &:hover {
              i {
                transform: translateX(@vw5-580);
              }
            }
          }
        }
      }
    }
  }
}

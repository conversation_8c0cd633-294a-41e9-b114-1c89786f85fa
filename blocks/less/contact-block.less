// out: ../css/contact-block.css, compress: true, strictMath: true
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less'; 
  
.contactBlock {
  color: @hardWhite; 
  .col {
      display: inline-block;
      position: relative;
      vertical-align: middle;
      margin-bottom: @vw40;
      width: 25%;
      .images {
        margin-left: -@vw8;
        width: calc(100% ~"+" @vw16);
        .imageWrapper {
          display: inline-block;
          margin: 0 @vw8;
          height: auto;
          width: calc(50% ~"-" @vw16);
          vertical-align: top;
          a {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            text-decoration: none;
            color: @hardWhite;
            cursor: pointer;
          }
          .innerImage {
            height: (@vw100 * 3) + @vw80;
            position: relative;
            overflow: hidden;
            video, img {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: cover;
              object-position: center;
            }
          }
        }
      }
      &:nth-child(1) {
        .imageWrapper {
          &:last-child {
            margin-top: @vw100 + @vw30;
            -webkit-mask-image: linear-gradient(90deg, #000,rgba(0, 0, 0, 0));
            mask-image: linear-gradient(90deg, #000,rgba(0, 0, 0, 0));
          }
        }
      }
      &:nth-child(2) {
        width: 50%;
        .formWrapper {
          padding: @vw100 @vw112 + @vw16 @vw50 @vw112 + @vw16;
          .rounded(@vw20);
          border: 2px dashed @hardWhite;
          .formIntro {
            text-align: center;
          }
          .field {
            display: block;
            &:not(:last-of-type) {
              margin-bottom: @vw20;
            }
            &.half {
              vertical-align: top;
              display: inline-block;
              width: 50%;
              padding-right: @vw20;
            }
            &.submit {
              input {
                background-color: @primaryColor;
                border: 2px solid @primaryColor;
                cursor: pointer;
                padding: @vw22 @vw22;
                .transition(.3s);
                -webkit-box-shadow: 0 0 @vw20 transparent;
                box-shadow: 0 0 @vw20 transparent;
                color: @hardWhite;
                text-align: center !important;
                &:hover {
                  transform: scale(.95);
                  -webkit-box-shadow: 0 @vw10 @vw50 @primaryColor;
                  box-shadow: 0 @vw10 @vw50 @primaryColor;
                }
              }
              .textLink {
                &:hover {
                  input {
                    padding-left: @vw30;
                    padding-right: 0;
                  }
                }
              }
            }
            label {
              display: block;
              font-size: @vw12;
              margin-bottom: @vw10;
            }
            input, textarea { 
              background-color: @almostWhite;
              font-family: 'Poppins', arial, sans-serif;
              font-size: @vw16;
              color: @hardBlack;
              font-weight: 400;
              line-height: @vw22;
              width: 100%;
              display: block;
              border: none;
              position: relative;
              overflow: hidden;
              text-overflow: ellipsis;
              padding: @vw15 @vw20;
            }
            textarea {
              resize: none;
            }
            .wpcf7-not-valid-tip {
              margin-top: @vw5;
            }
          }
        }
      }
      &:nth-child(3) {
        .imageWrapper {
          &:first-child {
            margin-top: @vw100 + @vw30;
            -webkit-mask-image: linear-gradient(-90deg, #000,rgba(0, 0, 0, 0));
            mask-image: linear-gradient(-90deg, #000,rgba(0, 0, 0, 0));
          }
        }
      }

  }
  .wpcf7-response-output, .wpcf7-not-valid-tip, .wpcf7 form.invalid .wpcf7-response-output, .wpcf7 form.unaccepted .wpcf7-response-output, .wpcf7 form.payment-required .wpcf7-response-output {
    background: rgba(255,0,0,.2);
    color: @primaryColor;
    .rounded(@vw5);
    padding: @vw5;
    font-size: @vw12;
    border: 1px solid @primaryColor;
  }
  .wpcf7 form.sent .wpcf7-response-output {
    background: rgba(0,255,0,.2);
    color: @secondaryColor;
    .rounded(@vw5);
    padding: @vw5;
    font-size: @vw12;
    border: 1px solid @secondaryColor;
  }
}

@media all and (max-width: 1160px) {
  .contactBlock {
    .col {
      margin-bottom: @vw40-1160;
      .images {
        margin-left: -@vw8-1160;
        width: calc(100% + @vw16-1160);
        .imageWrapper {
          margin: 0 @vw8-1160;
          width: calc(50% - @vw16-1160);
          .innerImage {
            height: (@vw100-1160 * 3) + @vw80-1160;
          }
        }
      }
      &:nth-child(1) .imageWrapper:last-child {
        margin-top: @vw100-1160 + @vw30-1160;
      }
      &:nth-child(2) {
        .formWrapper {
          padding: @vw100-1160 @vw40-1160 @vw50-1160 @vw40-1160;
          .field {
            &:not(:last-of-type) {
              margin-bottom: @vw20-1160;
            }
            &.half {
              padding-right: @vw20-1160;
            }
            &.submit input {
              padding: @vw22-1160 @vw22-1160;
              -webkit-box-shadow: 0 0 @vw20-1160 transparent;
              box-shadow: 0 0 @vw20-1160 transparent;
              &:hover {
                -webkit-box-shadow: 0 @vw10-1160 @vw50-1160 @primaryColor;
                box-shadow: 0 @vw10-1160 @vw50-1160 @primaryColor;
              }
            }
            label {
              font-size: @vw12-1160;
              margin-bottom: @vw10-1160;
            }
            input, textarea {
              font-size: @vw16-1160;
              line-height: @vw22-1160;
              padding: @vw15-1160 @vw20-1160;
            }
            .wpcf7-not-valid-tip {
              margin-top: @vw5-1160;
            }
          }
        }
      }
      &:nth-child(3) .imageWrapper:first-child {
        margin-top: @vw100-1160 + @vw30-1160;
      }
    }
    .wpcf7-response-output, .wpcf7-not-valid-tip, .wpcf7 form.invalid .wpcf7-response-output, .wpcf7 form.unaccepted .wpcf7-response-output, .wpcf7 form.payment-required .wpcf7-response-output {
      .rounded(@vw5-1160);
      padding: @vw5-1160;
      font-size: @vw12-1160;
    }
    .wpcf7 form.sent .wpcf7-response-output {
      .rounded(@vw5-1160);
      padding: @vw5-1160;
      font-size: @vw12-1160;
    }
  }
}

@media all and (max-width: 580px) {
  .contactBlock {
    .col {
      margin-bottom: @vw40-580;
      width: 5%;
      &:nth-child(2) {
        width: 90%;
      }
      .images {
        display: none;
      }
      &:nth-child(1) .imageWrapper:last-child {
        margin-top: @vw100-580 + @vw30-580;
      }
      &:nth-child(2) {
        .formWrapper {
          padding: @vw100-580 @vw22-580 @vw50-580 @vw22-580;
          .field {
            width: 100%;
            &:not(:last-of-type) {
              margin-bottom: @vw20-580;
            }
            &.half {
              width: 100%;
              padding-right: @vw20-580;
            }
            &.submit input {
              padding: @vw22-580 @vw22-580;
              -webkit-box-shadow: 0 0 @vw20-580 transparent;
              box-shadow: 0 0 @vw20-580 transparent;
              &:hover {
                -webkit-box-shadow: 0 @vw10-580 @vw50-580 @primaryColor;
                box-shadow: 0 @vw10-580 @vw50-580 @primaryColor;
              }
            }
            label {
              display: none;
            }
            input, textarea {
              font-size: @vw22-580;
              line-height: @vw22-580;
              padding: @vw15-580 @vw20-580;
            }
            .wpcf7-not-valid-tip {
              margin-top: @vw5-580;
            }
          }
        }
      }
      &:nth-child(3) .imageWrapper:first-child {
        margin-top: @vw100-580 + @vw30-580;
      }
    }
    .wpcf7-response-output, .wpcf7-not-valid-tip, .wpcf7 form.invalid .wpcf7-response-output, .wpcf7 form.unaccepted .wpcf7-response-output, .wpcf7 form.payment-required .wpcf7-response-output {
      .rounded(@vw5-580);
      padding: @vw5-580;
      font-size: @vw16-580;
    }
    .wpcf7 form.sent .wpcf7-response-output {
      .rounded(@vw5-580);
      padding: @vw5-580;
      font-size: @vw12-580;
    }
  }
}

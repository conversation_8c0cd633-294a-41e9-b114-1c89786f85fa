{"key": "group_record_of_the_week_home", "title": "Record of the Week Home", "fields": [{"key": "field_record_of_the_week_home_title", "label": "Block Title", "name": "block_title", "aria-label": "", "type": "text", "instructions": "Enter a title for this block", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Record of the Week", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_record_of_the_week_home_source", "label": "Record Source", "name": "record_source", "aria-label": "", "type": "radio", "instructions": "Choose whether to use the global record of the week or select manually", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"global": "Use Global Record of the Week", "collection": "Select from Collection", "manual": "Enter Manually"}, "allow_null": 0, "other_choice": 0, "default_value": "global", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_record_of_the_week_home_record", "label": "Select Record", "name": "selected_record", "aria-label": "", "type": "post_object", "instructions": "Select a record from your collection", "required": 0, "conditional_logic": [[{"field": "field_record_of_the_week_home_source", "operator": "==", "value": "collection"}]], "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["record"], "taxonomy": "", "allow_null": 0, "multiple": 0, "return_format": "object", "ui": 1, "bidirectional_target": []}, {"key": "field_record_of_the_week_home_manual_title", "label": "Record Title", "name": "manual_title", "aria-label": "", "type": "text", "instructions": "Enter the title of the record", "required": 0, "conditional_logic": [[{"field": "field_record_of_the_week_home_source", "operator": "==", "value": "manual"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_record_of_the_week_home_manual_artist", "label": "Artist", "name": "manual_artist", "aria-label": "", "type": "text", "instructions": "Enter the artist name", "required": 0, "conditional_logic": [[{"field": "field_record_of_the_week_home_source", "operator": "==", "value": "manual"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_record_of_the_week_home_manual_image", "label": "Record Image", "name": "manual_image", "aria-label": "", "type": "image", "instructions": "Upload an image of the record", "required": 0, "conditional_logic": [[{"field": "field_record_of_the_week_home_source", "operator": "==", "value": "manual"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_record_of_the_week_home_description", "label": "Description", "name": "description", "aria-label": "", "type": "textarea", "instructions": "Enter a description for the record of the week (will be truncated to 5 lines)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "br"}, {"key": "field_record_of_the_week_home_link", "label": "Link", "name": "link", "aria-label": "", "type": "true_false", "instructions": "Link to the record page (only works for records from collection)", "required": 0, "conditional_logic": [[{"field": "field_record_of_the_week_home_source", "operator": "==", "value": "collection"}]], "wrapper": {"width": "", "class": "", "id": ""}, "message": "Enable link to record page", "default_value": 1, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_record_of_the_week_home_manual_link", "label": "External Link", "name": "manual_link", "aria-label": "", "type": "url", "instructions": "Enter an optional external link (e.g., to Discogs or Bandcamp)", "required": 0, "conditional_logic": [[{"field": "field_record_of_the_week_home_source", "operator": "==", "value": "manual"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_record_of_the_week_home_background", "label": "Background Color", "name": "background_color", "aria-label": "", "type": "color_picker", "instructions": "Choose a background color for the block (optional)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "#1A1A1A", "enable_opacity": false, "return_format": "string"}, {"key": "field_record_of_the_week_home_vinyl_image", "label": "Vinyl image", "name": "vinyl_image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}, {"key": "field_record_of_the_week_home_button_link", "label": "Button Link", "name": "button_link", "aria-label": "", "type": "link", "instructions": "Optional button link to display at the bottom of the block", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}], "location": [[{"param": "block", "operator": "==", "value": "acf/record-of-the-week-home"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1747400803}
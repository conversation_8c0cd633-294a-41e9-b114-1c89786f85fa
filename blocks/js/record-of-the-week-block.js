$(document).ready(function () {
    $(document).on("initPage", function () {
        if ($(".recordOfTheWeekBlock").length > 0) {
            initSignatureAnimation();
        }
    });
});

function initSignatureAnimation() {
    if (typeof gsap === 'undefined' || !gsap.getProperty) {
        console.warn('GSAP or DrawSVG plugin not loaded');
        return;
    }

    // Find all signature SVGs
    const signatureSvgs = document.querySelectorAll('.signature-svg');

    if (signatureSvgs.length === 0) {
        return;
    }

    // Create a timeline for each signature
    signatureSvgs.forEach(svg => {
        // Get all paths in the SVG
        const paths = svg.querySelectorAll('.signature-path');

        // Set initial state - all paths invisible
        gsap.set(paths, { drawSVG: '0%' });

        // Create a timeline
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: svg,
                start: 'top 80%', // Start animation when the top of the SVG is 80% from the top of the viewport
                toggleActions: 'play none none none'
            }
        });

        // Add each path to the timeline with a staggered delay
        tl.to(paths, {
            drawSVG: '100%',
            duration: 2.5,
            ease: 'power1.inOut',
            stagger: 0.15
        });
    });
}

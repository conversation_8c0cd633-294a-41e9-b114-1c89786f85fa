<?php
/**
 * Block Name: Global Info Block
 * Description: A global block that displays the same content on every page where it's added
 */

// Block ID
$block_id = 'global-info-' . $block['id'];

// Block classes
$classes = 'globalInfoBlock';
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}

// Get location information
$location = rewindrecords_get_location();

// Get block fields
$title = get_field('title') ?: 'Kom bij ons langs';
$show_hours = get_field('show_hours');
$custom_hours = get_field('custom_hours');
$show_location_info = get_field('show_location_info');
$media_type = get_field('media_type') ?: 'image';
$image = get_field('image');
$map_marker_image = get_field('map_marker_image');
$map_image = get_field('map');
$links = get_field('links');
?>

<section id="<?php echo esc_attr($block_id); ?>" class="<?php echo esc_attr($classes); ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="cols">
            <!-- left Column: Content -->
            <div class="col contentCol">
                <div class="infoWrapper">
                    <?php if ($title) : ?>
                        <h2 class="normalTitle" data-lines data-words><?php echo esc_html($title); ?></h2>
                    <?php endif; ?>
                    
                    <div class="infoContent">
                        <?php if ($show_hours) : ?>
                            <div class="storeHours" data-lines data-words>
                                <?php if ($custom_hours) : ?>
                                    <?php echo wpautop($custom_hours); ?>
                                <?php else : ?>
                                    <p data-lines data-words>Maandag tot vrijdag open van 11:00 tot 20:00.<br>
                                    Zondag open van 10:00 tot 17:00</p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($show_location_info && $location && !empty($location['formatted_address'])) : ?>
                            <div class="storeLocation">
                                <p>5 min van station Eindhoven<br>
                                <?php echo esc_html($location['formatted_address']); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                    <?php if ($links) : ?>
                        <div class="links">
                            <?php foreach ($links as $link) : ?>
                                <a href="<?php echo esc_url($link['link_url']); ?>" class="textLink" <?php echo $link['link_target'] ? 'target="_blank" rel="noopener noreferrer"' : ''; ?>>
                                    <i class="icon-arrow-right"></i>
                                    <span class="innerMask">
                                        <span class="innerWrapper">
                                            <span class="innerText absolute"><?php echo esc_html($link['link_text']); ?></span>
                                            <span class="innerText absolute" aria-hidden="true"><?php echo esc_html($link['link_text']); ?></span>
                                        </span>
                                    </span>
                                    <span class="divider">
                                        <svg width="184.5" height="2" viewBox="0 0 184.5 2">
                                            <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                                        </svg>
                                    </span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php else : ?>
                        <!-- Default links if none are provided -->
                        <div class="links">
                            <a href="<?php echo esc_url($location['maps_link'] ?: '#'); ?>" class="textLink">
                                <i class="icon-arrow-right"></i>
                                <span class="innerMask">
                                    <span class="innerWrapper">
                                        <span class="innerText absolute">Routebeschrijving</span>
                                        <span class="innerText absolute" aria-hidden="true">Routebeschrijving</span>
                                    </span>
                                </span>
                                <span class="divider">
                                    <svg width="184.5" height="2" viewBox="0 0 184.5 2">
                                        <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                                    </svg>
                                </span>
                            </a>
                            <a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>" class="textLink">
                                <i class="icon-arrow-right"></i>
                                <span class="innerMask">
                                    <span class="innerWrapper">
                                        <span class="innerText absolute">Contact</span>
                                        <span class="innerText absolute" aria-hidden="true">Contact</span>
                                    </span>
                                </span>
                                <span class="divider">
                                    <svg width="184.5" height="2" viewBox="0 0 184.5 2">
                                        <line data-name="Line 6" x2="184.5" transform="translate(0 1)" fill="none" stroke="#eee" stroke-width="2"/>
                                    </svg>
                                </span>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- Right Column: Image or Map -->
            <div class="col imageCol" data-parallax data-parallax-speed="-1">
                <div class="mediaWrapper">
                    <?php if ($media_type === 'map') : ?>
                        <div class="mapWrapper">
                            <!-- Black map background -->
                            <div class="blackMap">
                                <img src="<?php echo esc_url($map_image['sizes']['large']); ?>" alt="<?php echo esc_attr($map_image['alt']); ?>" />
                            </div>
                            <!-- Map marker with popup -->
                            <?php if ($map_marker_image) : ?>
                                <div class="mapMarker">
                                    <div class="markerPin"></div>
                                    <div class="markerPopup">
                                        <img src="<?php echo esc_url($map_marker_image['sizes']['thumbnail']); ?>" alt="<?php echo esc_attr($map_marker_image['alt']); ?>" />
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php elseif ($image) : ?>
                        <div class="imageWrapper">
                            <img class="lazy" data-src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

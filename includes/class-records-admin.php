<?php
/**
 * Records Admin Settings
 */

class Rewindrecords_Records_Admin {

    /**
     * CSV Import instance
     */
    private $csv_import;

    /**
     * Constructor
     */
    public function __construct() {
        // Initialize CSV Import
        require_once get_template_directory() . '/includes/class-records-csv-import.php';
        $this->csv_import = new Rewindrecords_CSV_Import();

        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));



        // Add AJAX handlers
        add_action('wp_ajax_rewindrecords_update_record_data', array($this, 'ajax_update_record_data'));
        add_action('wp_ajax_rewindrecords_get_all_records', array($this, 'ajax_get_all_records'));
        add_action('wp_ajax_rewindrecords_update_record_release_date', array($this, 'ajax_update_record_release_date'));
        add_action('wp_ajax_rewindrecords_clear_log', array($this, 'ajax_clear_log'));
        add_action('wp_ajax_rewindrecords_download_log', array($this, 'ajax_download_log'));
        add_action('wp_ajax_rewindrecords_update_all_formats', array($this, 'ajax_update_all_formats'));
        add_action('wp_ajax_rewindrecords_update_single_format', array($this, 'ajax_update_single_format'));

        // Allow editors to access admin functionality
        add_action('admin_init', array($this, 'allow_editor_access'));

        // Add custom columns to record post type
        add_filter('manage_record_posts_columns', array($this, 'add_record_columns'));
        add_action('manage_record_posts_custom_column', array($this, 'display_record_column_content'), 10, 2);
        add_filter('manage_edit-record_sortable_columns', array($this, 'make_record_columns_sortable'));

        // Add quick edit fields
        add_action('quick_edit_custom_box', array($this, 'add_quick_edit_fields'), 10, 2);
        add_action('admin_footer-edit.php', array($this, 'add_quick_edit_javascript'));

        // Add bulk edit fields
        add_action('bulk_edit_custom_box', array($this, 'add_bulk_edit_fields'), 10, 2);

        // Save quick edit and bulk edit data
        add_action('save_post_record', array($this, 'save_record_data'), 10, 2);
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Rewindrecords', 'rewindrecords'),
            __('Rewindrecords', 'rewindrecords'),
            'edit_posts',
            'rewindrecords-csv-import',
            array($this, 'display_csv_import_page'),
            'dashicons-album',
            30
        );





        add_submenu_page(
            'rewindrecords-csv-import',
            __('CSV Import', 'rewindrecords'),
            __('CSV Import', 'rewindrecords'),
            'edit_posts',
            'rewindrecords-csv-import',
            array($this, 'display_csv_import_page')
        );

        add_submenu_page(
            'rewindrecords-csv-import',
            __('Import Log', 'rewindrecords'),
            __('Import Log', 'rewindrecords'),
            'edit_posts',
            'rewindrecords-import-log',
            array($this, 'display_import_log_page')
        );

        add_submenu_page(
            'rewindrecords-csv-import',
            __('Update Release Dates', 'rewindrecords'),
            __('Update Release Dates', 'rewindrecords'),
            'edit_posts',
            'rewindrecords-update-dates',
            array($this, 'display_update_dates_page')
        );
    }





    /**
     * Allow editors to access admin functionality
     */
    public function allow_editor_access() {
        $role = get_role('editor');
        if ($role) {
            $role->add_cap('edit_records');
            $role->add_cap('delete_records');
            $role->add_cap('edit_others_records');
            $role->add_cap('delete_others_records');
            $role->add_cap('publish_records');
            $role->add_cap('read_private_records');
        }
    }

    /**
     * Display CSV import page
     */
    public function display_csv_import_page() {
        if (!current_user_can('edit_posts')) {
            return;
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <div class="card">
                <h2><?php _e('CSV Import (Kassadump)', 'rewindrecords'); ?></h2>

                <!-- Discogs API Settings -->
                <div style="margin-bottom: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffc107; border-radius: 5px;">
                    <h3><?php _e('Discogs API Settings', 'rewindrecords'); ?></h3>
                    <form method="post" action="">
                        <?php wp_nonce_field('rewindrecords_save_discogs_token', 'discogs_token_nonce'); ?>
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Discogs Personal Access Token', 'rewindrecords'); ?></th>
                                <td>
                                    <input type="text" name="discogs_token" value="<?php echo esc_attr(get_option('rewindrecords_discogs_token', '')); ?>" class="regular-text" placeholder="<?php _e('Enter your Discogs token here', 'rewindrecords'); ?>" />
                                    <p class="description">
                                        <?php _e('Get your token from:', 'rewindrecords'); ?>
                                        <a href="https://www.discogs.com/settings/developers" target="_blank">https://www.discogs.com/settings/developers</a>
                                    </p>
                                </td>
                            </tr>
                        </table>
                        <p class="submit">
                            <input type="submit" name="save_discogs_token" class="button-primary" value="<?php _e('Save Token', 'rewindrecords'); ?>" />
                        </p>
                    </form>
                </div>

                <!-- API Test Buttons -->
                <div style="margin-bottom: 20px; padding: 15px; background: #f0f8ff; border: 1px solid #0073aa; border-radius: 5px;">
                    <h3><?php _e('API Test', 'rewindrecords'); ?></h3>
                    <p><?php _e('Test the Discogs API with known barcodes:', 'rewindrecords'); ?></p>
                    <button type="button" id="test-api" class="button"><?php _e('Test Discogs API (Portishead - Dummy)', 'rewindrecords'); ?></button>
                    <button type="button" id="test-turnstile" class="button" style="margin-left: 10px;"><?php _e('Test Turnstile Barcode', 'rewindrecords'); ?></button>
                    <button type="button" id="test-beyonce" class="button" style="margin-left: 10px;"><?php _e('Test Beyoncé Barcode', 'rewindrecords'); ?></button>
                    <div id="api-test-result" style="margin-top: 10px;"></div>
                </div>

                <!-- Bulk Format Update -->
                <div style="margin-bottom: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                    <h3><?php _e('Format Update', 'rewindrecords'); ?></h3>
                    <p><?php _e('Update records with Discogs format information (replaces "Nieuw Vinyl" with actual format like "Vinyl, LP, Album"):', 'rewindrecords'); ?></p>

                    <!-- Single Record Update -->
                    <div style="margin-bottom: 15px;">
                        <label for="single-barcode"><?php _e('Test Single Record:', 'rewindrecords'); ?></label>
                        <input type="text" id="single-barcode" placeholder="<?php _e('Enter barcode', 'rewindrecords'); ?>" style="margin: 0 10px;">
                        <button type="button" id="update-single-format" class="button"><?php _e('Update Single Record', 'rewindrecords'); ?></button>
                    </div>

                    <!-- Bulk Update -->
                    <button type="button" id="update-all-formats" class="button button-primary"><?php _e('Update All Records with Discogs Format', 'rewindrecords'); ?></button>
                    <div id="format-update-result" style="margin-top: 10px;"></div>
                </div>
                <p><?php _e('Upload a kassadump CSV file to import records into your collection.', 'rewindrecords'); ?></p>
                <p><?php _e('The system uses a two-step API lookup process for maximum accuracy:', 'rewindrecords'); ?></p>
                <ol>
                    <li><strong><?php _e('Discogs API:', 'rewindrecords'); ?></strong> <?php _e('Gets artist name and album title using barcode', 'rewindrecords'); ?></li>
                    <li><strong><?php _e('iTunes API:', 'rewindrecords'); ?></strong> <?php _e('Gets release date, album artwork, genre, and additional metadata', 'rewindrecords'); ?></li>
                </ol>
                <p><strong><?php _e('Benefits:', 'rewindrecords'); ?></strong> <?php _e('Discogs has better barcode coverage, iTunes provides high-quality metadata and artwork.', 'rewindrecords'); ?></p>
                <p><?php _e('The following information will be imported from the CSV file:', 'rewindrecords'); ?></p>
                <ul>
                    <li><?php _e('Article number (ArtikelNummer) - unique identifier', 'rewindrecords'); ?></li>
                    <li><?php _e('Barcode (EAN/UPC) - used for API lookup of artist, title, and artwork', 'rewindrecords'); ?></li>
                    <li><?php _e('Selling price including VAT (Verkoopprijs_incl) - displayed on website', 'rewindrecords'); ?></li>
                    <li><?php _e('Stock quantity (Voorraad) - for inventory management', 'rewindrecords'); ?></li>
                    <li><?php _e('Product category (ArtikelGroep) - only "Vinyl" products are imported', 'rewindrecords'); ?></li>
                    <li><?php _e('Product description (Omschrijving) - fallback for artist/title extraction', 'rewindrecords'); ?></li>
                    <li><?php _e('Release date (from column M)', 'rewindrecords'); ?></li>
                    <li><?php _e('Media type and units (LP, CD, etc.)', 'rewindrecords'); ?></li>
                </ul>
                <p><?php _e('Additionally, the following information will be automatically retrieved based on artist and album title:', 'rewindrecords'); ?></p>
                <ul>
                    <li><?php _e('Album covers (high resolution) via iTunes API or Cover Art Archive', 'rewindrecords'); ?></li>
                    <li><?php _e('Complete tracklist with track numbers and durations (via MusicBrainz)', 'rewindrecords'); ?></li>
                    <li><?php _e('Album description with genre, release date, label, and other metadata in Dutch', 'rewindrecords'); ?></li>
                </ul>
                <p><?php _e('The system will first try to find album covers via iTunes API, and if not found, will use MusicBrainz + Cover Art Archive as a fallback.', 'rewindrecords'); ?></p>
                <p><?php _e('The CSV file should have the following format (using semicolons as separators):', 'rewindrecords'); ?></p>
                <pre>ArtikelNummer;Barcode;Bestelnr;ArtikelGroep;Leverancier;Omschrijving;Inkoopprijs_excl;Verkoopprijs_excl;Verkoopprijs_incl;Marge;Voorraad;Min_Voorraad;Bestellen;Btw;Aktie_perc;Tot_AktieDatum;OpPrijsLijst;DatumPrijsWijziging1;PrijsWijziging1;DatumPrijsWijziging2;PrijsWijziging2;DatumPrijsWijziging3;PrijsWijziging3;Eenheid;AantalLegeRegels;OpRegel;ArtikelNummerNa;Atn:VragenOm;Atn:VragenOmBedrag;Atn:VragenOmBedragNeg;Atn:VragenOmAantal;Atn:VragenOmOmschrijving;Atn:TegenBoekenPin;Atn:TegenBoekenContant;Atn:AantalOvernemen;Atn:KortingBtwCheck;Atn:KortingGroepOvernemen;Atn:VoorraadBijwerken;Atn:Spaarpunten;Atn:GeenKortingKlant;Atn:Aktie_bedrag;Atn:Oud;Atn:MargeArtikel;Atn:OmsVet</pre>
                <p><?php _e('Important notes:', 'rewindrecords'); ?></p>
                <ul>
                    <li><?php _e('Use semicolons (;) as field separators, not commas', 'rewindrecords'); ?></li>
                    <li><?php _e('Artist names with format "LASTNAME, FIRSTNAME" will be converted to "Firstname Lastname"', 'rewindrecords'); ?></li>
                    <li><?php _e('Artist names with numbering like "Turnstile (2)" will be cleaned to "Turnstile"', 'rewindrecords'); ?></li>
                    <li><?php _e('Format information from Discogs (e.g., "Vinyl, LP, Album, Limited Edition") replaces generic "Nieuw Vinyl"', 'rewindrecords'); ?></li>
                    <li><?php _e('The price including VAT (Verkoopprijs_incl) will be displayed on the website', 'rewindrecords'); ?></li>
                    <li><?php _e('Existing records are updated quickly without API calls for better performance', 'rewindrecords'); ?></li>
                    <li><?php _e('Only new records require API lookups, making re-imports much faster', 'rewindrecords'); ?></li>
                    <li><?php _e('Records with missing artist or title information will be skipped', 'rewindrecords'); ?></li>
                    <li><?php _e('Check the Import Log for detailed processing information', 'rewindrecords'); ?></li>
                </ul>

                <form id="csv-import-form" enctype="multipart/form-data">
                    <p>
                        <label for="csv-file"><?php _e('Select CSV File:', 'rewindrecords'); ?></label>
                        <input type="file" name="csv_file" id="csv-file" accept=".csv" required>
                    </p>

                    <div id="csv-import-controls">
                        <button type="submit" class="button button-primary" id="start-csv-import"><?php _e('Import Records', 'rewindrecords'); ?></button>
                        <button type="button" class="button" id="cancel-csv-import" style="display: none; margin-left: 10px;"><?php _e('Cancel Import', 'rewindrecords'); ?></button>
                    </div>
                </form>

                <div id="csv-import-progress" style="display: none; margin-top: 20px;">
                    <h3><?php _e('Importing CSV...', 'rewindrecords'); ?></h3>
                    <div class="progress-bar" style="width: 100%; background-color: #f1f1f1; border-radius: 5px; margin-bottom: 15px; position: relative;">
                        <div class="progress" style="width: 0%; height: 30px; background: linear-gradient(90deg, #0073aa, #005a87); border-radius: 5px; transition: width 0.3s; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 14px; position: relative;">
                            <span class="progress-text">0%</span>
                        </div>
                    </div>
                    <p class="progress-status" style="margin: 10px 0; font-style: italic; color: #0073aa;"><?php _e('Preparing import...', 'rewindrecords'); ?></p>
                    <div class="progress-details" style="background: #f9f9f9; padding: 15px; border-radius: 5px; border-left: 4px solid #0073aa;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin-bottom: 10px;">
                            <div><strong><?php _e('Total:', 'rewindrecords'); ?></strong> <span class="total-count">-</span></div>
                            <div><strong><?php _e('Processed:', 'rewindrecords'); ?></strong> <span class="processed-count">0</span></div>
                            <div><strong><?php _e('New:', 'rewindrecords'); ?></strong> <span class="new-count">0</span></div>
                            <div><strong><?php _e('Updated:', 'rewindrecords'); ?></strong> <span class="updated-count">0</span></div>
                            <div><strong><?php _e('Errors:', 'rewindrecords'); ?></strong> <span class="error-count">0</span></div>
                        </div>
                        <div class="current-record" style="font-size: 12px; color: #666; font-style: italic;"></div>
                    </div>
                </div>

                <div id="csv-import-results" style="display: none; margin-top: 20px;">
                    <h3><?php _e('Import Results', 'rewindrecords'); ?></h3>
                    <p class="import-summary"></p>
                    <div class="import-details" style="margin-top: 10px;">
                        <p><strong><?php _e('New records:', 'rewindrecords'); ?></strong> <span class="new-records-count">0</span></p>
                        <p><strong><?php _e('Updated records:', 'rewindrecords'); ?></strong> <span class="updated-records-count">0</span></p>
                        <p><strong><?php _e('Total processed:', 'rewindrecords'); ?></strong> <span class="total-records-count">0</span></p>
                    </div>
                    <div class="import-errors" style="display: none; margin-top: 15px;">
                        <h4><?php _e('Errors', 'rewindrecords'); ?></h4>
                        <ul class="error-list"></ul>
                    </div>
                </div>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    var totalRecords = 0;
                    var processedRecords = 0;
                    var newRecords = 0;
                    var updatedRecords = 0;
                    var errorCount = 0;
                    var allErrors = [];
                    var currentBatchId = null;
                    var importCancelled = false;

                    $('#csv-import-form').on('submit', function(e) {
                        e.preventDefault();

                        var $form = $(this);
                        var formData = new FormData($form[0]);
                        formData.append('action', 'rewindrecords_parse_csv');
                        formData.append('nonce', '<?php echo wp_create_nonce('rewindrecords_csv_import'); ?>');

                        var $controls = $('#csv-import-controls');
                        var $progress = $('#csv-import-progress');
                        var $results = $('#csv-import-results');

                        // Reset counters
                        totalRecords = 0;
                        processedRecords = 0;
                        newRecords = 0;
                        updatedRecords = 0;
                        errorCount = 0;
                        allErrors = [];

                        // Reset UI
                        $controls.hide();
                        $results.hide();
                        $progress.show();
                        $('#cancel-csv-import').show();
                        importCancelled = false;
                        $('.progress-status').text('<?php _e('Parsing CSV file...', 'rewindrecords'); ?>');

                        // First, parse the CSV
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(response) {
                                if (response.success) {
                                    totalRecords = response.data.total_records;
                                    currentBatchId = response.data.batch_id;
                                    $('.total-count').text(totalRecords);

                                    // Show not found count if any
                                    if (response.data.not_found_count > 0) {
                                        $('.progress-status').text('<?php _e('Starting import...', 'rewindrecords'); ?> (' + response.data.not_found_count + ' <?php _e('records not found in API', 'rewindrecords'); ?>)');
                                    } else {
                                        $('.progress-status').text('<?php _e('Starting import...', 'rewindrecords'); ?>');
                                    }

                                    // Start batch processing
                                    processBatch(response.data.batch_id, 0);
                                } else {
                                    showError(response.data);
                                }
                            },
                            error: function() {
                                showError('<?php _e('Failed to parse CSV file. Please try again.', 'rewindrecords'); ?>');
                            }
                        });
                    });

                    function processBatch(batchId, offset) {
                        // Check if import was cancelled
                        if (importCancelled) {
                            showError('<?php _e('Import was cancelled by user.', 'rewindrecords'); ?>');
                            return;
                        }

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_import_batch',
                                batch_id: batchId,
                                offset: offset,
                                nonce: '<?php echo wp_create_nonce('rewindrecords_csv_import'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    var data = response.data;

                                    if (data.completed) {
                                        // Import completed
                                        completeImport(data.not_found);
                                    } else {
                                        // Update counters
                                        processedRecords += data.processed;
                                        newRecords += data.new_records;
                                        updatedRecords += data.updated_records;

                                        if (data.errors && data.errors.length > 0) {
                                            allErrors = allErrors.concat(data.errors);
                                            errorCount += data.errors.length;
                                        }

                                        // Update progress
                                        updateProgress();

                                        // Process next batch
                                        processBatch(batchId, data.next_offset);
                                    }
                                } else {
                                    // Check if import was cancelled
                                    if (response.data && response.data.includes('cancelled')) {
                                        showError('<?php _e('Import was cancelled because a new import was started.', 'rewindrecords'); ?>');
                                    } else {
                                        showError(response.data);
                                    }
                                }
                            },
                            error: function() {
                                showError('<?php _e('Import failed. Please try again.', 'rewindrecords'); ?>');
                            }
                        });
                    }

                    function updateProgress() {
                        var progressPercent = totalRecords > 0 ? (processedRecords / totalRecords) * 100 : 0;
                        $('.progress').css('width', progressPercent + '%');
                        $('.progress-text').text(Math.round(progressPercent) + '%');

                        $('.processed-count').text(processedRecords);
                        $('.new-count').text(newRecords);
                        $('.updated-count').text(updatedRecords);
                        $('.error-count').text(errorCount);

                        $('.progress-status').text('<?php _e('Processing records...', 'rewindrecords'); ?> (' + processedRecords + '/' + totalRecords + ')');
                    }

                    function completeImport(notFoundRecords) {
                        var $progress = $('#csv-import-progress');
                        var $results = $('#csv-import-results');
                        var $controls = $('#csv-import-controls');

                        // Final progress update
                        $('.progress').css('width', '100%');
                        $('.progress-text').text('100%');

                        // Show results
                        $progress.hide();
                        $results.show();

                        var message = '<?php _e('Successfully imported {new} new records and updated {updated} existing records from CSV.', 'rewindrecords'); ?>';
                        message = message.replace('{new}', newRecords).replace('{updated}', updatedRecords);
                        $('.import-summary').text(message);

                        // Update counts
                        $('.new-records-count').text(newRecords);
                        $('.updated-records-count').text(updatedRecords);
                        $('.total-records-count').text(newRecords + updatedRecords);

                        // Show errors if any
                        if (allErrors.length > 0) {
                            var $errorList = $('.error-list');
                            $errorList.empty();
                            $.each(allErrors, function(i, error) {
                                $errorList.append('<li>' + error + '</li>');
                            });
                            $('.import-errors').show();
                        } else {
                            $('.import-errors').hide();
                        }

                        // Show not found records if any
                        if (notFoundRecords && notFoundRecords.length > 0) {
                            var $notFoundSection = $('.import-not-found');
                            if ($notFoundSection.length === 0) {
                                $results.append('<div class="import-not-found" style="margin-top: 15px;"><h4><?php _e('Records not found in API', 'rewindrecords'); ?></h4><ul class="not-found-list"></ul></div>');
                                $notFoundSection = $('.import-not-found');
                            }

                            var $notFoundList = $('.not-found-list');
                            $notFoundList.empty();
                            $.each(notFoundRecords, function(i, record) {
                                $notFoundList.append('<li><strong>' + record.article_id + '</strong> (Barcode: ' + record.barcode + ') - ' + record.description + '</li>');
                            });
                            $notFoundSection.show();
                        }

                        $controls.show();
                    }

                    function showError(message) {
                        var $progress = $('#csv-import-progress');
                        var $results = $('#csv-import-results');
                        var $controls = $('#csv-import-controls');

                        $progress.hide();
                        $results.show();
                        $('.import-summary').text(message);
                        $('.import-errors').hide();
                        $('#cancel-csv-import').hide();
                        $controls.show();
                    }

                    // Cancel import button
                    $('#cancel-csv-import').on('click', function() {
                        if (confirm('<?php _e('Are you sure you want to cancel the import?', 'rewindrecords'); ?>')) {
                            importCancelled = true;
                            $(this).prop('disabled', true).text('<?php _e('Cancelling...', 'rewindrecords'); ?>');

                            // Send cancel request to server
                            $.ajax({
                                url: ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'rewindrecords_cancel_import',
                                    batch_id: currentBatchId,
                                    nonce: '<?php echo wp_create_nonce('rewindrecords_csv_import'); ?>'
                                },
                                complete: function() {
                                    showError('<?php _e('Import cancelled by user.', 'rewindrecords'); ?>');
                                }
                            });
                        }
                    });

                    // API Test button
                    $('#test-api').on('click', function() {
                        var $button = $(this);
                        var $result = $('#api-test-result');

                        $button.prop('disabled', true).text('<?php _e('Testing...', 'rewindrecords'); ?>');
                        $result.html('<p><?php _e('Testing Discogs API...', 'rewindrecords'); ?></p>');

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_test_api',
                                barcode: '0602537972050',
                                nonce: '<?php echo wp_create_nonce('rewindrecords_csv_import'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $result.html('<div style="color: green;"><strong><?php _e('SUCCESS:', 'rewindrecords'); ?></strong> ' + response.data.message + '</div>');
                                } else {
                                    $result.html('<div style="color: red;"><strong><?php _e('FAILED:', 'rewindrecords'); ?></strong> ' + response.data + '</div>');
                                }
                            },
                            error: function() {
                                $result.html('<div style="color: red;"><strong><?php _e('ERROR:', 'rewindrecords'); ?></strong> <?php _e('Request failed', 'rewindrecords'); ?></div>');
                            },
                            complete: function() {
                                $button.prop('disabled', false).text('<?php _e('Test Discogs API (Portishead - Dummy)', 'rewindrecords'); ?>');
                            }
                        });
                    });

                    // Turnstile Test button
                    $('#test-turnstile').on('click', function() {
                        var $button = $(this);
                        var $result = $('#api-test-result');

                        $button.prop('disabled', true).text('<?php _e('Testing...', 'rewindrecords'); ?>');
                        $result.html('<p><?php _e('Testing Turnstile barcode...', 'rewindrecords'); ?></p>');

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_test_api',
                                barcode: '0075678643118',
                                nonce: '<?php echo wp_create_nonce('rewindrecords_csv_import'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $result.html('<div style="color: green;"><strong><?php _e('SUCCESS:', 'rewindrecords'); ?></strong> ' + response.data.message + '</div>');
                                } else {
                                    $result.html('<div style="color: red;"><strong><?php _e('FAILED:', 'rewindrecords'); ?></strong> ' + response.data + '</div>');
                                }
                            },
                            error: function() {
                                $result.html('<div style="color: red;"><strong><?php _e('ERROR:', 'rewindrecords'); ?></strong> <?php _e('Request failed', 'rewindrecords'); ?></div>');
                            },
                            complete: function() {
                                $button.prop('disabled', false).text('<?php _e('Test Turnstile Barcode', 'rewindrecords'); ?>');
                            }
                        });
                    });

                    // Beyoncé Test button
                    $('#test-beyonce').on('click', function() {
                        var $button = $(this);
                        var $result = $('#api-test-result');

                        $button.prop('disabled', true).text('<?php _e('Testing...', 'rewindrecords'); ?>');
                        $result.html('<p><?php _e('Testing Beyoncé barcode...', 'rewindrecords'); ?></p>');

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_test_api',
                                barcode: '0196588929113', // Beyoncé Cowboy Carter barcode from your data
                                nonce: '<?php echo wp_create_nonce('rewindrecords_csv_import'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $result.html('<div style="color: green;"><strong><?php _e('SUCCESS:', 'rewindrecords'); ?></strong> ' + response.data.message + '</div>');
                                } else {
                                    $result.html('<div style="color: red;"><strong><?php _e('FAILED:', 'rewindrecords'); ?></strong> ' + response.data + '</div>');
                                }
                            },
                            error: function() {
                                $result.html('<div style="color: red;"><strong><?php _e('ERROR:', 'rewindrecords'); ?></strong> <?php _e('Request failed', 'rewindrecords'); ?></div>');
                            },
                            complete: function() {
                                $button.prop('disabled', false).text('<?php _e('Test Beyoncé Barcode', 'rewindrecords'); ?>');
                            }
                        });
                    });

                    // Single Format Update button
                    $('#update-single-format').on('click', function() {
                        var $button = $(this);
                        var $result = $('#format-update-result');
                        var barcode = $('#single-barcode').val().trim();

                        if (!barcode) {
                            alert('<?php _e('Please enter a barcode', 'rewindrecords'); ?>');
                            return;
                        }

                        $button.prop('disabled', true).text('<?php _e('Updating...', 'rewindrecords'); ?>');
                        $result.html('<p style="color: blue;"><?php _e('Updating record with barcode: ', 'rewindrecords'); ?>' + barcode + '</p>');

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_update_single_format',
                                barcode: barcode,
                                nonce: '<?php echo wp_create_nonce('rewindrecords_csv_import'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $result.html('<div style="color: green;"><strong><?php _e('SUCCESS:', 'rewindrecords'); ?></strong> ' + response.data.message + '</div>');
                                } else {
                                    $result.html('<div style="color: red;"><strong><?php _e('FAILED:', 'rewindrecords'); ?></strong> ' + response.data + '</div>');
                                }
                            },
                            error: function() {
                                $result.html('<div style="color: red;"><strong><?php _e('ERROR:', 'rewindrecords'); ?></strong> <?php _e('Request failed', 'rewindrecords'); ?></div>');
                            },
                            complete: function() {
                                $button.prop('disabled', false).text('<?php _e('Update Single Record', 'rewindrecords'); ?>');
                            }
                        });
                    });

                    // Bulk Format Update button
                    $('#update-all-formats').on('click', function() {
                        var $button = $(this);
                        var $result = $('#format-update-result');

                        if (!confirm('<?php _e('This will update ALL records with Discogs format information. This may take a long time. Continue?', 'rewindrecords'); ?>')) {
                            return;
                        }

                        $button.prop('disabled', true).text('<?php _e('Updating...', 'rewindrecords'); ?>');
                        $result.html('<p style="color: blue;"><?php _e('Updating all records with Discogs format information...', 'rewindrecords'); ?></p>');

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_update_all_formats',
                                nonce: '<?php echo wp_create_nonce('rewindrecords_csv_import'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $result.html('<div style="color: green;"><strong><?php _e('SUCCESS:', 'rewindrecords'); ?></strong> ' + response.data.message + '</div>');
                                } else {
                                    $result.html('<div style="color: red;"><strong><?php _e('FAILED:', 'rewindrecords'); ?></strong> ' + response.data + '</div>');
                                }
                            },
                            error: function() {
                                $result.html('<div style="color: red;"><strong><?php _e('ERROR:', 'rewindrecords'); ?></strong> <?php _e('Request failed', 'rewindrecords'); ?></div>');
                            },
                            complete: function() {
                                $button.prop('disabled', false).text('<?php _e('Update All Records with Discogs Format', 'rewindrecords'); ?>');
                            }
                        });
                    });
                });
            </script>
        </div>
        <?php
    }



    /**
     * Add custom columns to record post type
     *
     * @param array $columns Existing columns
     * @return array Modified columns
     */
    public function add_record_columns($columns) {
        $new_columns = array();

        // Insert columns after title
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;

            if ($key === 'title') {
                $new_columns['article_id'] = __('Article ID', 'rewindrecords');
                $new_columns['barcode'] = __('Barcode', 'rewindrecords');
                $new_columns['price'] = __('Price', 'rewindrecords');
                $new_columns['stock'] = __('Stock', 'rewindrecords');
                $new_columns['product_type'] = __('Product Type', 'rewindrecords');
            }
        }

        return $new_columns;
    }

    /**
     * Display content for custom columns
     *
     * @param string $column Column name
     * @param int $post_id Post ID
     */
    public function display_record_column_content($column, $post_id) {
        switch ($column) {
            case 'article_id':
                $article_id = get_post_meta($post_id, '_record_article_id', true);
                echo esc_html($article_id);
                break;

            case 'barcode':
                $barcode = get_post_meta($post_id, '_record_barcode', true);
                if (empty($barcode)) {
                    // Fallback to old naming
                    $barcode = get_post_meta($post_id, '_record_ean_code', true);
                }
                echo esc_html($barcode);
                break;

            case 'price':
                $price = get_post_meta($post_id, '_record_price', true);
                if (!empty($price)) {
                    echo '€ ' . number_format((float)$price, 2, ',', '.');
                } else {
                    echo '—';
                }
                break;

            case 'stock':
                $stock = get_post_meta($post_id, '_record_stock', true);
                if ($stock !== '') {
                    echo esc_html($stock);
                } else {
                    echo '0';
                }
                break;

            case 'product_type':
                $terms = get_the_terms($post_id, 'record_product_type');
                if ($terms && !is_wp_error($terms)) {
                    $product_types = array();
                    foreach ($terms as $term) {
                        $product_types[] = $term->name;
                    }
                    echo esc_html(implode(', ', $product_types));
                } else {
                    echo __('Album', 'rewindrecords'); // Default to Album if not set
                }
                break;
        }
    }

    /**
     * Make custom columns sortable
     *
     * @param array $columns Sortable columns
     * @return array Modified sortable columns
     */
    public function make_record_columns_sortable($columns) {
        $columns['article_id'] = 'article_id';
        $columns['barcode'] = 'barcode';
        $columns['price'] = 'price';
        $columns['stock'] = 'stock';
        $columns['product_type'] = 'product_type';

        return $columns;
    }

    /**
     * Add quick edit fields
     *
     * @param string $column_name Column name
     * @param string $post_type Post type
     */
    public function add_quick_edit_fields($column_name, $post_type) {
        if ($post_type !== 'record') {
            return;
        }

        if ($column_name !== 'price') {
            return;
        }

        ?>
        <fieldset class="inline-edit-col-right">
            <div class="inline-edit-col">
                <label>
                    <span class="title"><?php _e('Price', 'rewindrecords'); ?></span>
                    <span class="input-text-wrap">
                        <input type="text" name="record_price" class="regular-text" value="">
                    </span>
                </label>
                <label>
                    <span class="title"><?php _e('Stock', 'rewindrecords'); ?></span>
                    <span class="input-text-wrap">
                        <input type="number" name="record_stock" class="small-text" value="" min="0">
                    </span>
                </label>
                <input type="hidden" name="record_data_nonce" value="<?php echo wp_create_nonce('record_data_nonce'); ?>">
            </div>
        </fieldset>
        <?php
    }

    /**
     * Add bulk edit fields
     *
     * @param string $column_name Column name
     * @param string $post_type Post type
     */
    public function add_bulk_edit_fields($column_name, $post_type) {
        if ($post_type !== 'record') {
            return;
        }

        if ($column_name !== 'price') {
            return;
        }

        ?>
        <fieldset class="inline-edit-col-right">
            <div class="inline-edit-col">
                <label>
                    <span class="title"><?php _e('Price', 'rewindrecords'); ?></span>
                    <span class="input-text-wrap">
                        <input type="text" name="record_price" class="regular-text" value="">
                    </span>
                </label>
                <label>
                    <span class="title"><?php _e('Stock', 'rewindrecords'); ?></span>
                    <span class="input-text-wrap">
                        <input type="number" name="record_stock" class="small-text" value="" min="0">
                    </span>
                </label>
                <input type="hidden" name="record_data_nonce" value="<?php echo wp_create_nonce('record_data_nonce'); ?>">
            </div>
        </fieldset>
        <?php
    }

    /**
     * Add JavaScript for quick edit
     */
    public function add_quick_edit_javascript() {
        global $post_type;

        if ($post_type !== 'record') {
            return;
        }

        ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Quick Edit
                $('.editinline').on('click', function() {
                    var post_id = $(this).closest('tr').attr('id');
                    post_id = post_id.replace('post-', '');

                    var $row = $(this).closest('tr');
                    var price = $row.find('td.price').text().replace('€ ', '').replace('.', '').replace(',', '.');
                    var stock = $row.find('td.stock').text();

                    // Set values in quick edit form
                    $('#edit-' + post_id).find('input[name="record_price"]').val(price);
                    $('#edit-' + post_id).find('input[name="record_stock"]').val(stock);
                });

                // AJAX update for quick edit
                $(document).on('click', '#bulk_edit, .save', function() {
                    var $button = $(this);

                    // If this is not a record post type, return
                    if ($button.closest('form').find('input[name="post_type"]').val() !== 'record') {
                        return;
                    }

                    // If this is a bulk edit
                    if ($button.attr('id') === 'bulk_edit') {
                        var price = $('input[name="record_price"]').val();
                        var stock = $('input[name="record_stock"]').val();

                        // If both fields are empty, return
                        if (price === '' && stock === '') {
                            return;
                        }

                        // Get selected post IDs
                        var post_ids = [];
                        $('input[name="post[]"]:checked').each(function() {
                            post_ids.push($(this).val());
                        });

                        // Update records via AJAX
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_update_record_data',
                                post_ids: post_ids,
                                price: price,
                                stock: stock,
                                nonce: $('input[name="record_data_nonce"]').val()
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Reload the page
                                    location.reload();
                                }
                            }
                        });
                    }
                });
            });
        </script>
        <?php
    }

    /**
     * Save record data from quick edit and bulk edit
     *
     * @param int $post_id Post ID
     * @param WP_Post $post Post object
     */
    public function save_record_data($post_id, $post) {
        // If this is an autosave, our form has not been submitted, so we don't want to do anything
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check if our nonce is set
        if (!isset($_POST['record_data_nonce'])) {
            return;
        }

        // Verify that the nonce is valid
        if (!wp_verify_nonce($_POST['record_data_nonce'], 'record_data_nonce')) {
            return;
        }

        // Check the user's permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save price
        if (isset($_POST['record_price']) && $_POST['record_price'] !== '') {
            $price = sanitize_text_field($_POST['record_price']);
            $price = str_replace(',', '.', $price);
            update_post_meta($post_id, '_record_price', floatval($price));
        }

        // Save stock
        if (isset($_POST['record_stock']) && $_POST['record_stock'] !== '') {
            $stock = intval($_POST['record_stock']);
            update_post_meta($post_id, '_record_stock', $stock);
        }
    }

    /**
     * AJAX handler for updating record data
     */
    public function ajax_update_record_data() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'record_data_nonce')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Check if we have post IDs
        if (!isset($_POST['post_ids']) || !is_array($_POST['post_ids'])) {
            wp_send_json_error(__('No records selected', 'rewindrecords'));
        }

        $post_ids = array_map('intval', $_POST['post_ids']);
        $price = isset($_POST['price']) ? sanitize_text_field($_POST['price']) : '';
        $stock = isset($_POST['stock']) ? intval($_POST['stock']) : '';

        // Update each post
        foreach ($post_ids as $post_id) {
            // Check if the post exists and is a record
            $post = get_post($post_id);
            if (!$post || $post->post_type !== 'record') {
                continue;
            }

            // Update price
            if ($price !== '') {
                $price_clean = str_replace(',', '.', $price);
                update_post_meta($post_id, '_record_price', floatval($price_clean));
            }

            // Update stock
            if ($stock !== '') {
                update_post_meta($post_id, '_record_stock', $stock);
            }
        }

        wp_send_json_success();
    }

    /**
     * Display update dates page
     */
    public function display_update_dates_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <div class="card">
                <h2><?php _e('Update Release Dates for Records', 'rewindrecords'); ?></h2>
                <p><?php _e('This tool will update all records with accurate release dates from iTunes API and MusicBrainz.', 'rewindrecords'); ?></p>
                <p><?php _e('The process will:', 'rewindrecords'); ?></p>
                <ul>
                    <li><?php _e('Scan all records in your collection', 'rewindrecords'); ?></li>
                    <li><?php _e('Try to find accurate release dates from iTunes API first', 'rewindrecords'); ?></li>
                    <li><?php _e('If not found, try MusicBrainz API as a fallback', 'rewindrecords'); ?></li>
                    <li><?php _e('Update the release date metadata for each record', 'rewindrecords'); ?></li>
                </ul>
                <p><?php _e('This will ensure that your "Latest Releases" section shows the most accurate and up-to-date information.', 'rewindrecords'); ?></p>

                <div id="update-dates-controls">
                    <button type="button" class="button button-primary" id="start-update-dates"><?php _e('Update Release Dates', 'rewindrecords'); ?></button>
                </div>

                <div id="update-dates-progress" style="display: none; margin-top: 20px;">
                    <div class="progress-bar" style="height: 20px; background-color: #f1f1f1; margin-bottom: 10px;">
                        <div class="progress" style="width: 0%; height: 100%; background-color: #0073aa;"></div>
                    </div>
                    <p class="progress-status"><?php _e('Updating release dates...', 'rewindrecords'); ?></p>
                    <p class="progress-count"><span class="current">0</span> / <span class="total">0</span> <?php _e('records processed', 'rewindrecords'); ?></p>
                </div>

                <div id="update-dates-results" style="display: none; margin-top: 20px;">
                    <h3><?php _e('Update Results', 'rewindrecords'); ?></h3>
                    <p class="update-summary"></p>
                    <div class="update-details" style="margin-top: 10px;">
                        <p><strong><?php _e('Records updated:', 'rewindrecords'); ?></strong> <span class="updated-records-count">0</span></p>
                        <p><strong><?php _e('Records already up-to-date:', 'rewindrecords'); ?></strong> <span class="unchanged-records-count">0</span></p>
                        <p><strong><?php _e('Total processed:', 'rewindrecords'); ?></strong> <span class="total-records-count">0</span></p>
                    </div>
                </div>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    var recordsToProcess = [];
                    var currentIndex = 0;
                    var totalRecords = 0;
                    var updatedRecords = 0;
                    var unchangedRecords = 0;

                    $('#start-update-dates').on('click', function() {
                        var $controls = $('#update-dates-controls');
                        var $progress = $('#update-dates-progress');
                        var $results = $('#update-dates-results');

                        // Reset UI
                        $controls.hide();
                        $results.hide();
                        $progress.show();

                        // Get all records
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_get_all_records',
                                nonce: '<?php echo wp_create_nonce('rewindrecords_update_dates'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    recordsToProcess = response.data;
                                    totalRecords = recordsToProcess.length;

                                    // Update progress UI
                                    $('.progress-count .total').text(totalRecords);

                                    // Start processing records
                                    processNextRecord();
                                } else {
                                    $progress.hide();
                                    $controls.show();
                                    alert('<?php _e('Error: Could not retrieve records.', 'rewindrecords'); ?>');
                                }
                            },
                            error: function() {
                                $progress.hide();
                                $controls.show();
                                alert('<?php _e('Error: Could not retrieve records.', 'rewindrecords'); ?>');
                            }
                        });
                    });

                    function processNextRecord() {
                        if (currentIndex >= recordsToProcess.length) {
                            // All records processed
                            completeUpdate();
                            return;
                        }

                        var recordId = recordsToProcess[currentIndex];

                        // Update progress UI
                        var progressPercent = (currentIndex / totalRecords) * 100;
                        $('.progress-bar .progress').css('width', progressPercent + '%');
                        $('.progress-count .current').text(currentIndex + 1);

                        // Process record
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_update_record_release_date',
                                record_id: recordId,
                                nonce: '<?php echo wp_create_nonce('rewindrecords_update_dates'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    if (response.data.updated) {
                                        updatedRecords++;
                                    } else {
                                        unchangedRecords++;
                                    }
                                }

                                // Process next record
                                currentIndex++;
                                processNextRecord();
                            },
                            error: function() {
                                // Process next record even if there's an error
                                currentIndex++;
                                processNextRecord();
                            }
                        });
                    }

                    function completeUpdate() {
                        var $progress = $('#update-dates-progress');
                        var $results = $('#update-dates-results');
                        var $controls = $('#update-dates-controls');

                        // Update progress UI to 100%
                        $('.progress-bar .progress').css('width', '100%');

                        // Hide progress and show results
                        $progress.hide();
                        $results.show();
                        $controls.show();

                        // Update results
                        $('.updated-records-count').text(updatedRecords);
                        $('.unchanged-records-count').text(unchangedRecords);
                        $('.total-records-count').text(totalRecords);

                        // Update summary
                        var summaryText = '<?php _e('Successfully updated release dates for {0} records.', 'rewindrecords'); ?>';
                        summaryText = summaryText.replace('{0}', updatedRecords);
                        $('.update-summary').text(summaryText);

                        // Reset variables for next run
                        recordsToProcess = [];
                        currentIndex = 0;
                        totalRecords = 0;
                        updatedRecords = 0;
                        unchangedRecords = 0;
                    }
                });
            </script>
        </div>
        <?php
    }

    /**
     * AJAX handler for getting all records
     */
    public function ajax_get_all_records() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_update_dates')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Get all record IDs
        $args = array(
            'post_type' => 'record',
            'posts_per_page' => -1,
            'fields' => 'ids',
        );

        $query = new WP_Query($args);

        if ($query->have_posts()) {
            wp_send_json_success($query->posts);
        } else {
            wp_send_json_error(__('No records found', 'rewindrecords'));
        }
    }

    /**
     * AJAX handler for updating a record's release date
     */
    public function ajax_update_record_release_date() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_update_dates')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Get record ID
        $record_id = isset($_POST['record_id']) ? intval($_POST['record_id']) : 0;

        if (!$record_id) {
            wp_send_json_error(__('Invalid record ID', 'rewindrecords'));
        }

        // Get record data
        $post = get_post($record_id);

        if (!$post || $post->post_type !== 'record') {
            wp_send_json_error(__('Record not found', 'rewindrecords'));
        }

        // Get artist and title
        $artist = '';
        $title = $post->post_title;

        // Get artist from taxonomy
        $artists = get_the_terms($record_id, 'record_artist');
        if ($artists && !is_wp_error($artists) && !empty($artists)) {
            $artist = $artists[0]->name;
        }

        // Check if we already have a release date
        $current_release_date = get_post_meta($record_id, '_record_release_date', true);
        $updated = false;

        // Try to get release date from iTunes API
        if ($this->update_release_date_from_itunes($record_id, $artist, $title)) {
            $updated = true;
        }
        // If iTunes fails, try MusicBrainz
        else if ($this->update_release_date_from_musicbrainz($record_id, $artist, $title)) {
            $updated = true;
        }
        // If both fail, ensure we have at least a basic release date
        else {
            // Use the CSV import class to ensure a release date
            require_once get_template_directory() . '/includes/class-records-csv-import.php';
            $csv_import = new Rewindrecords_CSV_Import();
            $csv_import->ensure_release_date($record_id);

            // Check if the release date was updated
            $new_release_date = get_post_meta($record_id, '_record_release_date', true);
            $updated = ($new_release_date !== $current_release_date);
        }

        wp_send_json_success(array(
            'updated' => $updated,
            'record_id' => $record_id,
        ));
    }

    /**
     * Update album info from iTunes API
     *
     * @param int $record_id Record ID
     * @param string $artist Artist name
     * @param string $album Album title
     * @return bool True if updated, false otherwise
     */
    private function update_release_date_from_itunes($record_id, $artist, $album) {
        // Build search term
        $search_term = urlencode($artist . ' ' . $album);

        // Build iTunes API URL
        $api_url = 'https://itunes.apple.com/search?term=' . $search_term . '&media=music&entity=album&limit=1';

        // Set request arguments
        $args = array(
            'timeout' => 15,
        );

        // Make request to iTunes API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data) || empty($data['results']) || count($data['results']) === 0) {
            return false;
        }

        // Get the first result
        $album_data = $data['results'][0];

        // Check if we have a release date
        if (empty($album_data['releaseDate'])) {
            return false;
        }

        // Get current release date
        $current_release_date = get_post_meta($record_id, '_record_release_date', true);

        // Get new release date
        $new_release_date = substr($album_data['releaseDate'], 0, 10); // Get YYYY-MM-DD part

        // Update release date if different
        $updated = false;
        if ($new_release_date !== $current_release_date) {
            update_post_meta($record_id, '_record_release_date', $new_release_date);
            $updated = true;
        }

        // Try to get tracklist and additional info
        if (!empty($album_data['collectionId'])) {
            $this->get_album_details_from_itunes($record_id, $album_data['collectionId']);
        }

        return $updated;
    }

    /**
     * Get album details from iTunes API including tracklist
     *
     * @param int $post_id Post ID
     * @param string $collection_id iTunes collection ID
     * @return bool True on success, false on failure
     */
    private function get_album_details_from_itunes($post_id, $collection_id) {
        // Build iTunes API URL for tracks
        $api_url = 'https://itunes.apple.com/lookup?id=' . $collection_id . '&entity=song';

        // Set request arguments
        $args = array(
            'timeout' => 15,
        );

        // Make request to iTunes API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data) || empty($data['results']) || count($data['results']) < 2) {
            return false;
        }

        // The first result is the album, the rest are tracks
        $album_data = $data['results'][0];
        $tracks = array_slice($data['results'], 1);

        // Process tracks
        $tracklist = array();
        foreach ($tracks as $track) {
            if (isset($track['trackName'])) {
                // Format duration from milliseconds to MM:SS
                $duration = '';
                if (isset($track['trackTimeMillis'])) {
                    $seconds = floor($track['trackTimeMillis'] / 1000);
                    $minutes = floor($seconds / 60);
                    $seconds = $seconds % 60;
                    $duration = sprintf('%d:%02d', $minutes, $seconds);
                }

                $track_data = array(
                    'position' => isset($track['trackNumber']) ? $track['trackNumber'] : count($tracklist) + 1,
                    'title' => $track['trackName'],
                    'duration' => $duration,
                );
                $tracklist[] = $track_data;
            }
        }

        // Save tracklist if we have tracks and there isn't one already
        $current_tracklist = get_post_meta($post_id, '_record_tracklist', true);
        if (!empty($tracklist) && empty($current_tracklist)) {
            update_post_meta($post_id, '_record_tracklist', $tracklist);
        }

        // Save additional metadata
        if (!empty($album_data['copyright'])) {
            update_post_meta($post_id, '_record_copyright', $album_data['copyright']);
        }

        // Generate a Dutch description for the album if there isn't content already
        $post = get_post($post_id);
        if ($post && empty($post->post_content)) {
            $this->generate_dutch_description_from_itunes($post_id, $album_data, $tracks);
        }

        return true;
    }

    /**
     * Generate a Dutch description for the album from iTunes data
     *
     * @param int $post_id Post ID
     * @param array $album_data Album data from iTunes
     * @param array $tracks Tracks data from iTunes
     * @return void
     */
    private function generate_dutch_description_from_itunes($post_id, $album_data, $tracks) {
        // Use the helper class to generate the description
        Rewindrecords_Helper::generate_dutch_description_from_itunes($post_id, $album_data, $tracks);
    }



    /**
     * Update release date from MusicBrainz API
     *
     * @param int $record_id Record ID
     * @param string $artist Artist name
     * @param string $album Album title
     * @return bool True if updated, false otherwise
     */
    private function update_release_date_from_musicbrainz($record_id, $artist, $album) {
        // Sanitize search terms
        $search_term = urlencode('artist:' . $artist . ' AND release:' . $album);

        // Build MusicBrainz API URL
        $api_url = 'https://musicbrainz.org/ws/2/release/?query=' . $search_term . '&fmt=json&limit=1';

        // Set user agent as required by MusicBrainz API
        $args = array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'RewindrecordsApp/1.0 ( https://rewindrecords.com )',
            ),
        );

        // Make request to MusicBrainz API
        $response = wp_remote_get($api_url, $args);

        // Check for errors
        if (is_wp_error($response)) {
            return false;
        }

        // Parse response
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Check if we got results
        if (empty($data['releases']) || count($data['releases']) === 0) {
            return false;
        }

        // Get the first release
        $release = $data['releases'][0];

        // Check if we have a release date
        if (empty($release['date'])) {
            return false;
        }

        // Get current release date
        $current_release_date = get_post_meta($record_id, '_record_release_date', true);

        // Get new release date
        $new_release_date = $release['date'];

        // Make sure the release date is in the correct format (YYYY-MM-DD)
        if (preg_match('/^\d{4}$/', $new_release_date)) {
            $new_release_date = $new_release_date . '-01-01';
        } elseif (preg_match('/^\d{4}-\d{2}$/', $new_release_date)) {
            $new_release_date = $new_release_date . '-01';
        }

        // Update release date if different
        if ($new_release_date !== $current_release_date) {
            update_post_meta($record_id, '_record_release_date', $new_release_date);
            return true;
        }

        return false;
    }

    /**
     * Display import log page
     */
    public function display_import_log_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Import Log', 'rewindrecords'); ?></h1>

            <div style="margin-bottom: 20px;">
                <button type="button" id="refresh-log" class="button"><?php _e('Refresh Log', 'rewindrecords'); ?></button>
                <button type="button" id="clear-log" class="button button-secondary" style="margin-left: 10px;"><?php _e('Clear Log', 'rewindrecords'); ?></button>
                <button type="button" id="download-log" class="button button-secondary" style="margin-left: 10px;"><?php _e('Download Log', 'rewindrecords'); ?></button>
            </div>

            <div id="log-content" style="background: #f1f1f1; padding: 15px; border: 1px solid #ccc; height: 600px; overflow-y: auto; font-family: monospace; font-size: 12px; white-space: pre-wrap;">
                <?php echo esc_html($this->get_import_log()); ?>
            </div>
        </div>

        <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Refresh log
                $('#refresh-log').on('click', function() {
                    location.reload();
                });

                // Clear log
                $('#clear-log').on('click', function() {
                    if (confirm('<?php _e('Are you sure you want to clear the import log?', 'rewindrecords'); ?>')) {
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'rewindrecords_clear_log',
                                nonce: '<?php echo wp_create_nonce('rewindrecords_log'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $('#log-content').html('<?php _e('Log cleared.', 'rewindrecords'); ?>');
                                } else {
                                    alert('<?php _e('Failed to clear log.', 'rewindrecords'); ?>');
                                }
                            }
                        });
                    }
                });

                // Download log
                $('#download-log').on('click', function() {
                    window.open('<?php echo admin_url('admin-ajax.php?action=rewindrecords_download_log&nonce=' . wp_create_nonce('rewindrecords_log')); ?>');
                });

                // Auto-scroll to bottom
                var logContent = document.getElementById('log-content');
                logContent.scrollTop = logContent.scrollHeight;
            });
        </script>
        <?php
    }

    /**
     * Get import log content
     */
    private function get_import_log() {
        $log_file = WP_CONTENT_DIR . '/rewindrecords-debug.log';

        if (!file_exists($log_file)) {
            return __('No log file found.', 'rewindrecords');
        }

        // Get last 1000 lines to prevent memory issues
        $lines = file($log_file);
        if (count($lines) > 1000) {
            $lines = array_slice($lines, -1000);
            $content = "... (showing last 1000 lines)\n\n" . implode('', $lines);
        } else {
            $content = file_get_contents($log_file);
        }

        return $content;
    }

    /**
     * AJAX handler for clearing log
     */
    public function ajax_clear_log() {
        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'rewindrecords_log')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__('Insufficient permissions', 'rewindrecords'));
        }

        $log_file = WP_CONTENT_DIR . '/rewindrecords-debug.log';

        if (file_exists($log_file)) {
            file_put_contents($log_file, '');
        }

        wp_send_json_success();
    }

    /**
     * AJAX handler for downloading log
     */
    public function ajax_download_log() {
        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'rewindrecords_log')) {
            wp_die(__('Security check failed', 'rewindrecords'));
        }

        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'rewindrecords'));
        }

        $log_file = WP_CONTENT_DIR . '/rewindrecords-debug.log';

        if (!file_exists($log_file)) {
            wp_die(__('Log file not found', 'rewindrecords'));
        }

        // Set headers for download
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="rewindrecords-import-log-' . date('Y-m-d-H-i-s') . '.txt"');
        header('Content-Length: ' . filesize($log_file));

        // Output file content
        readfile($log_file);
        exit;
    }

    /**
     * AJAX handler for updating all records with Discogs format
     */
    public function ajax_update_all_formats() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_csv_import')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__('Insufficient permissions', 'rewindrecords'));
        }

        // Get CSV import instance
        $csv_import = new Rewindrecords_CSV_Import();

        // Update all records
        $result = $csv_import->update_all_records_with_discogs_format();

        $message = sprintf(
            __('Updated %d records with Discogs format information. %d records failed to update.', 'rewindrecords'),
            $result['updated'],
            $result['failed']
        );

        wp_send_json_success(array('message' => $message));
    }

    /**
     * AJAX handler for updating single record with Discogs format
     */
    public function ajax_update_single_format() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rewindrecords_csv_import')) {
            wp_send_json_error(__('Security check failed', 'rewindrecords'));
        }

        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__('Insufficient permissions', 'rewindrecords'));
        }

        $barcode = sanitize_text_field($_POST['barcode']);

        if (empty($barcode)) {
            wp_send_json_error(__('No barcode provided', 'rewindrecords'));
        }

        // Find record by barcode (check both meta keys)
        $args = array(
            'post_type' => 'record',
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => '_record_barcode',
                    'value' => $barcode,
                    'compare' => '='
                ),
                array(
                    'key' => '_record_ean_code',
                    'value' => $barcode,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1
        );

        $records = get_posts($args);

        if (empty($records)) {
            wp_send_json_error(__('No record found with barcode: ', 'rewindrecords') . $barcode);
        }

        $record = $records[0];

        // Get CSV import instance
        $csv_import = new Rewindrecords_CSV_Import();

        // Get Discogs format
        $discogs_data = $csv_import->lookup_discogs_by_barcode($barcode);

        if ($discogs_data && !empty($discogs_data['format'])) {
            // Update with Discogs format
            $format_terms = array_map('trim', explode(',', $discogs_data['format']));
            wp_set_object_terms($record->ID, $format_terms, 'record_format');

            $message = sprintf(
                __('Updated "%s" with format: %s', 'rewindrecords'),
                $record->post_title,
                $discogs_data['format']
            );

            wp_send_json_success(array('message' => $message));
        } else {
            wp_send_json_error(__('No format information found in Discogs for barcode: ', 'rewindrecords') . $barcode);
        }
    }
}
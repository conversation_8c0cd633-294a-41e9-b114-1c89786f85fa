<?php
/**
 * Rewindrecords Theme Functions
 */

// Include required files
require_once get_template_directory() . '/includes/post-types.php';
require_once get_template_directory() . '/includes/class-records-helper.php';
require_once get_template_directory() . '/includes/class-records-api.php';
require_once get_template_directory() . '/includes/class-records-admin.php';
require_once get_template_directory() . '/includes/class-records-meta.php';
require_once get_template_directory() . '/includes/class-records-customizer.php';
require_once get_template_directory() . '/includes/class-records-description.php';

// Initialize Records Admin
$records_admin = new Rewindrecords_Records_Admin();

// Initialize Records Meta
$records_meta = new Rewindrecords_Records_Meta();

// Initialize Records Customizer
$records_customizer = new Rewindrecords_Customizer();

// Initialize Records Description
$records_description = new Rewindrecords_Records_Description();

/**
 * Check for maintenance mode and display the maintenance page if enabled
 */
function rewindrecords_check_maintenance_mode() {
    if (rewindrecords_is_maintenance_mode()) {
        include(get_template_directory() . '/maintenance-mode.php');
        exit();
    }
}
add_action('template_redirect', 'rewindrecords_check_maintenance_mode', 1);

// Add ACF JSON save point
add_filter('acf/settings/save_json', 'rewindrecords_acf_json_save_point');
function rewindrecords_acf_json_save_point($path) {
    // Update path
    $path = get_stylesheet_directory() . '/acf-json';

    // Create path if it doesn't exist
    if (!file_exists($path)) {
        mkdir($path, 0755, true);
    }

    // Return path
    return $path;
}

// Add ACF JSON load point
add_filter('acf/settings/load_json', 'rewindrecords_acf_json_load_point');
function rewindrecords_acf_json_load_point($paths) {
    // Remove original path
    unset($paths[0]);

    // Append our path
    $paths[] = get_stylesheet_directory() . '/acf-json';

    // Return paths
    return $paths;
}

// Add ACF options page
if (function_exists('acf_add_options_page')) {
    acf_add_options_page(array(
        'page_title'    => 'Theme Settings',
        'menu_title'    => 'Theme Settings',
        'menu_slug'     => 'theme-settings',
        'capability'    => 'edit_posts',
        'redirect'      => false
    ));
}

function jn_enqueue_assets() {
    $scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'SPLITTEXT' => '/libs/SplitText.min.js',
        'Flickity' => '/libs/flickity.pkgd.min.js',
        'Hammer' => '/libs/hammer.min.js',
        'main_js' => '/assets/js/main.js',
        'Header' => '/assets/js/header.js',
        'Menu' => '/assets/js/parts/menu.js',
        'Parallax' => '/assets/js/parts/parallax.js',
        'Gallery' => '/assets/js/parts/gallery.js',
        'Slider' => '/assets/js/parts/slider.js',
        'Marquee' => '/assets/js/parts/marquee.js',
        'Split' => '/assets/js/parts/split.js',
        'Flickity_Custom' => '/assets/js/parts/flickity-custom.js',
        'Records_Archive' => '/assets/js/parts/records-archive.js',
    );

    foreach ($scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    $blocks = array(
        'HOME HEADER BLOCK' => '/blocks/js/home-header-block.js',
        'TEXT SLIDER BLOCK' => '/blocks/js/text-slider-block.js',
        'RANDOM RECORDS SLIDER BLOCK' => '/blocks/js/random-records-slider-block.js',
    );

    foreach ($blocks as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    // Enqueue GSAP
    wp_enqueue_script('gsap', 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js', array(), '3.11.4', true);
    wp_enqueue_script('gsap-drawsvg', 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/DrawSVGPlugin.min.js', array('gsap'), '3.11.4', true);
    wp_enqueue_script('gsap-scrolltrigger', 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js', array('gsap'), '3.11.4', true);

    // Enqueue Google Fonts: Bebas Neue for headings and Lora for body text
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap', array(), null);

    // Enqueue Font Awesome for icons
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');

    // Enqueue Flickity CSS
    wp_enqueue_style('flickity', get_theme_file_uri('/libs/flickity.min.css'), array(), '2.3.0');

    // Enqueue custom Flickity styles
    wp_enqueue_style('flickity-custom', get_theme_file_uri('/assets/css/flickity-custom.css'), array('flickity'), '1.0');

    // Enqueue Record Single CSS
    if (is_singular('record')) {
        wp_enqueue_style('record-single-style', get_theme_file_uri('/css/record-single.css'), array(), '1.0');
    }

    // Enqueue Select2 CSS
    wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '4.1.0');
    wp_enqueue_style('select2-custom', get_theme_file_uri('/assets/css/select2-custom.css'), array('select2'), '1.0');

    wp_enqueue_style('main', get_stylesheet_uri());
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add favicon
function ilc_favicon() {
    echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
    $sections = array(
        'customTheme-main-callout-title' => 'Title',
        'customTheme-main-callout-description' => 'Description',
        'customTheme-main-callout-featured-image' => 'Image',
        'customTheme-main-callout-noise' => 'Noise',
        'customTheme-main-callout-background' => 'Background image',
        'customTheme-main-callout-logo' => 'Logo',
        'customTheme-main-callout-logo-white' => 'Logo (White)',
        'customTheme-main-callout-telephone' => 'Telephone',
        'customTheme-main-callout-telephone-label' => 'Telephone label',
        'customTheme-main-callout-mail' => 'Mail',
        'customTheme-main-callout-street' => 'Street',
        'customTheme-main-callout-city' => 'City',
        'customTheme-main-callout-maps' => 'Maps',
        'customTheme-main-callout-facebook' => 'Facebook URL',
        'customTheme-main-callout-linkedin' => 'LinkedIn URL',
        'customTheme-main-callout-tiktok' => 'Tiktok URL',
        'customTheme-main-callout-instagram' => 'Instagram URL',
        'customTheme-main-callout-analytics' => 'Analytics ID',
        'customTheme-main-callout-company-information' => 'Company Information',
        'customTheme-main-callout-kvk' => 'KVK Nummer',
        'customTheme-main-callout-btw' => 'BTW Nummer',
        'customTheme-main-callout-iban' => 'IBAN Nummer',
        'customTheme-main-callout-eindhoven-logo' => 'This is Eindhoven Logo',
        'customTheme-main-callout-terms-page' => 'Algemene Voorwaarden Pagina',
        'customTheme-main-callout-privacy-page' => 'Privacy Statement Pagina'
    );

    $wp_customize->add_section('customTheme-main-callout-section', array(
        'title' => 'Main Information'
    ));

    foreach ($sections as $setting_id => $label) {
        $wp_customize->add_setting($setting_id);
        $control_args = array(
            'label' => $label,
            'section' => 'customTheme-main-callout-section',
            'settings' => $setting_id
        );

        // Voeg media-velden toe voor afbeelding, logo, noise en background image
        if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false || $setting_id === 'customTheme-main-callout-noise' || $setting_id === 'customTheme-main-callout-background' || $setting_id === 'customTheme-main-callout-eindhoven-logo') {
            $control_args['width'] = 750;
            $control_args['height'] = 500;
            $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
        }
        // Voeg tekstgebied toe voor 'Description' en 'Company Information'
        elseif ($label === 'Description' || $label === 'Company Information') {
            $control_args['type'] = 'textarea';
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        }
        // Voeg pagina dropdown toe voor terms en privacy pagina's
        elseif ($setting_id === 'customTheme-main-callout-terms-page' || $setting_id === 'customTheme-main-callout-privacy-page') {
            $control_args['type'] = 'dropdown-pages';
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        }
        // Voeg reguliere invoervelden toe voor andere instellingen
        else {
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        }
    }
}

add_action('customize_register', 'jn_customize_register');

// Register menus
function jn_register_menus() {
    register_nav_menus(array(
        'footer-menu' => 'footer-menu',
        'primary-menu' => 'Primary Menu',
    ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
    unset($robots['max-image-preview']);
    return $robots;
}

// blocks

add_action('acf/init', 'my_acf_blocks_init');
function my_acf_blocks_init() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

        // Register a testimonial block.
        acf_register_block_type(array(
            'name'              => 'home_header_block',
            'title'             => __('Home header Block'),
            'render_template'   => 'blocks/home-header-block.php',
            'category'          => 'headers',
        ));
        acf_register_block_type(array(
            'name'              => 'projects_home_block',
            'title'             => __('Projects Home Block'),
            'render_template'   => 'blocks/projects-home-block.php',
            'category'          => 'projects home block',
        ));
        acf_register_block_type(array(
            'name'              => 'projects_block',
            'title'             => __('Projects Block'),
            'render_template'   => 'blocks/projects-block.php',
            'category'          => 'projects block',
        ));
        acf_register_block_type(array(
            'name'              => 'image_quote_block',
            'title'             => __('Image Quote Block'),
            'render_template'   => 'blocks/image-quote-block.php',
            'category'          => 'image quote block',
        ));
        acf_register_block_type(array(
            'name'              => 'small_header_block',
            'title'             => __('Small header Block'),
            'render_template'   => 'blocks/small-header-block.php',
            'category'          => 'Small header block',
        ));
        acf_register_block_type(array(
            'name'              => 'small_header_links_block',
            'title'             => __('Small Header Links Block', 'rewindrecords'),
            'description'       => __('Display a centered header with huge title and multiple links', 'rewindrecords'),
            'render_template'   => 'blocks/small-header-links-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'heading',
            'keywords'          => array('header', 'title', 'links', 'centered'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));
        acf_register_block_type(array(
            'name'              => 'text_block',
            'title'             => __('Text Block'),
            'render_template'   => 'blocks/text-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'text_marquee_block',
            'title'             => __('Text marquee Block'),
            'render_template'   => 'blocks/text-marquee-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'small_text_marquee_block',
            'title'             => __('Small text marquee Block'),
            'render_template'   => 'blocks/small-text-marquee-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'text_slider_block',
            'title'             => __('Text Slider Block'),
            'render_template'   => 'blocks/text-slider-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'project_info_block',
            'title'             => __('Project Info Block'),
            'render_template'   => 'blocks/project-info-block.php',
            'category'          => 'project info block',
        ));
        acf_register_block_type(array(
            'name'              => 'project_header_block',
            'title'             => __('Project Header Block'),
            'render_template'   => 'blocks/project-header-block.php',
            'category'          => 'project header block',
        ));
        acf_register_block_type(array(
            'name'              => 'links_block',
            'title'             => __('Links Block'),
            'render_template'   => 'blocks/links-block.php',
            'category'          => 'links block',
        ));
        acf_register_block_type(array(
            'name'              => 'text_links_block',
            'title'             => __('Text Links Block'),
            'render_template'   => 'blocks/text-links-block.php',
            'category'          => 'text links block',
        ));

        // Register Records Block
        acf_register_block_type(array(
            'name'              => 'records_block',
            'title'             => __('Records Block', 'rewindrecords'),
            'description'       => __('Display records from your collection', 'rewindrecords'),
            'render_template'   => 'blocks/records-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'album',
            'keywords'          => array('records', 'vinyl', 'discogs', 'music'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));

        // Register Random Records Slider Block
        acf_register_block_type(array(
            'name'              => 'random_records_slider',
            'title'             => __('Random Records Slider', 'rewindrecords'),
            'description'       => __('Display a slider with random records from your collection', 'rewindrecords'),
            'render_template'   => 'blocks/random-records-slider-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'slides',
            'keywords'          => array('records', 'vinyl', 'slider', 'random', 'carousel'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
            'enqueue_assets'    => function() {
                wp_enqueue_script('random-records-slider-js', get_template_directory_uri() . '/blocks/js/random-records-slider-block.js', array('jquery', 'Flickity'), '', true);
            },
        ));

        // Register Latest Records Block
        acf_register_block_type(array(
            'name'              => 'latest_records',
            'title'             => __('Latest Records (What\'s New)', 'rewindrecords'),
            'description'       => __('Display the most recent records from your collection', 'rewindrecords'),
            'render_template'   => 'blocks/latest-records-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'update',
            'keywords'          => array('records', 'vinyl', 'latest', 'new', 'recent'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));

        // Register Record of the Week Block
        acf_register_block_type(array(
            'name'              => 'record_of_the_week',
            'title'             => __('Record of the Week', 'rewindrecords'),
            'description'       => __('Display a featured record of the week', 'rewindrecords'),
            'render_template'   => 'blocks/record-of-the-week-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'star-filled',
            'keywords'          => array('records', 'vinyl', 'featured', 'week', 'highlight'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
            'enqueue_assets'    => function() {
                wp_enqueue_style('record-of-the-week-css', get_template_directory_uri() . '/blocks/css/record-of-the-week-block.css', array(), '1.0');
                wp_enqueue_script('record-of-the-week-js', get_template_directory_uri() . '/blocks/js/record-of-the-week-block.js', array('jquery', 'gsap', 'gsap-drawsvg', 'gsap-scrolltrigger'), '', true);
            },
        ));

        // Register Other Products Block
        acf_register_block_type(array(
            'name'              => 'other_products',
            'title'             => __('Other Products', 'rewindrecords'),
            'description'       => __('Display non-album products like games, merchandise, etc.', 'rewindrecords'),
            'render_template'   => 'blocks/other-products-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'products',
            'keywords'          => array('products', 'games', 'merchandise', 'other'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));

        // Register Contact Block
        acf_register_block_type(array(
            'name'              => 'contact_block',
            'title'             => __('Contact Block', 'rewindrecords'),
            'description'       => __('Display a contact form with random record covers', 'rewindrecords'),
            'render_template'   => 'blocks/contact-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'email',
            'keywords'          => array('contact', 'form', 'email', 'records'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));

        // Register Rewind Home Header Block
        acf_register_block_type(array(
            'name'              => 'rewind_home_header',
            'title'             => __('Rewind Home Header', 'rewindrecords'),
            'description'       => __('Display a header with title, text, location, links, and media', 'rewindrecords'),
            'render_template'   => 'blocks/rewind-home-header-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'align-left',
            'keywords'          => array('header', 'home', 'title', 'location', 'media'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));

        // Register Media Text Block
        acf_register_block_type(array(
            'name'              => 'media_text_block',
            'title'             => __('Media Text Block', 'rewindrecords'),
            'description'       => __('Display media (image or video) on one side and text content on the other', 'rewindrecords'),
            'render_template'   => 'blocks/media-text-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'align-pull-left',
            'keywords'          => array('media', 'text', 'image', 'video', 'content'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));

        // Register Spotify Text Block
        acf_register_block_type(array(
            'name'              => 'spotify_text_block',
            'title'             => __('Spotify Text Block', 'rewindrecords'),
            'description'       => __('Display a Spotify embed on one side and text content on the other', 'rewindrecords'),
            'render_template'   => 'blocks/spotify-text-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'playlist-audio',
            'keywords'          => array('spotify', 'music', 'playlist', 'embed', 'audio'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));

        // Register Images Text Block
        acf_register_block_type(array(
            'name'              => 'images_text_block',
            'title'             => __('Images Text Block', 'rewindrecords'),
            'description'       => __('Display a three-column layout with images/videos and text content', 'rewindrecords'),
            'render_template'   => 'blocks/images-text-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'layout',
            'keywords'          => array('images', 'text', 'columns', 'video', 'content'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));

        // Register About Block with Interactive Vinyl
        acf_register_block_type(array(
            'name'              => 'about_block',
            'title'             => __('About Block', 'rewindrecords'),
            'description'       => __('Display an interactive vinyl record with text content', 'rewindrecords'),
            'render_template'   => 'blocks/about-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'album',
            'keywords'          => array('about', 'vinyl', 'record', 'interactive', 'rotate', 'scratch'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
            'enqueue_assets'    => function() {
                wp_enqueue_script('about-block-js', get_template_directory_uri() . '/blocks/js/about-block.js', array('jquery', 'gsap', 'gsap-scrolltrigger'), '', true);
            },
        ));

        // Register Global Info Block
        acf_register_block_type(array(
            'name'              => 'global_info_block',
            'title'             => __('Global Info Block', 'rewindrecords'),
            'description'       => __('A global block that displays the same content on every page where it\'s added', 'rewindrecords'),
            'render_template'   => 'blocks/global-info-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'location',
            'keywords'          => array('global', 'info', 'location', 'hours', 'contact', 'map'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
        ));

        // Register Partners Block
        acf_register_block_type(array(
            'name'              => 'partners_block',
            'title'             => __('Partners Block', 'rewindrecords'),
            'description'       => __('Display a list of partners with logos, names, and links', 'rewindrecords'),
            'render_template'   => 'blocks/partners-block.php',
            'category'          => 'rewindrecords',
            'icon'              => 'networking',
            'keywords'          => array('partners', 'logos', 'sponsors', 'collaborators'),
            'supports'          => array(
                'align' => array('wide', 'full'),
            ),
            'enqueue_style'     => get_template_directory_uri() . '/assets/css/blocks/partners-block.css',
        ));
    }
}

function render_button($field_name) {
    $link = get_field($field_name);

    if (is_array($link) && isset($link['url'], $link['title'])) {
        $link_url = esc_url($link['url']);
        $link_title = esc_html($link['title']);
        $link_target = !empty($link['target']) ? esc_attr($link['target']) : '_self';

        echo '<a class="button" href="' . $link_url . '" title="' . esc_attr($link_title) . '">
            <span class="innerText">' . $link_title . '</span>
            <span class="arrows">
                <i class="icon-arrow-right-up"></i>
                <i class="icon-arrow-right-up"></i>
            </span>
        </a>';
    }
}


function render_text_link($field_name) {
    $link = get_field($field_name);
    if ($link) {
        $link_url = $link['url'];
        $link_title = $link['title'];
        $link_target = $link['target'] ? $link['target'] : '_self';
        echo '<a href="' . esc_url($link_url) . '" title="' . esc_html($link_title) . '" class="textLink" target="' . esc_attr($link_target) . '">
                <span class="innerText">' . esc_html($link_title) . '</span>
                <span class="arrows">
                    <i class="icon-arrow-right-up"></i>
                    <i class="icon-arrow-right-up"></i>
                </span>
              </a>';
    }
}

function render_text_link_sub($field_name) {
    $link = get_sub_field($field_name);
    if ($link) {
        $link_url = $link['url'];
        $link_title = $link['title'];
        $link_target = $link['target'] ? $link['target'] : '_self';
        echo '<a href="' . esc_url($link_url) . '" title="' . esc_html($link_title) . '" class="textLink" target="' . esc_attr($link_target) . '">
                <span class="innerText">' . esc_html($link_title) . '</span>
                <span class="arrows">
                    <i class="icon-arrow-right-up"></i>
                    <i class="icon-arrow-right-up"></i>
                </span>
              </a>';
    }
}

function custom_theme_setup() {
    add_image_size('project-thumb-mobile', 640, 800, true);
    add_image_size('project-thumb-large', 640, 800, true);
    add_image_size('record-cover', 500, 500, true);
    add_image_size('record-cover-thumbnail', 300, 300, true);

    // Add theme support for post thumbnails
    add_theme_support('post-thumbnails');
}
add_action('after_setup_theme', 'custom_theme_setup');

function get_random_projects() {
    static $random_projects = null;

    if ($random_projects === null) {
        $random_projects = new WP_Query(array(
            'posts_per_page' => 5,
            'orderby'        => 'rand',
            'post_type'      => 'project',
        ));
    }

    return $random_projects;
}

/**
 * AJAX handler for filtering records
 */
function rewindrecords_filter_records() {
    // Check nonce for security
    check_ajax_referer('records_filter_nonce', 'nonce');

    // Get filter values
    $genre = isset($_POST['genre']) && $_POST['genre'] !== 'all' ? sanitize_text_field($_POST['genre']) : '';
    $artist = isset($_POST['artist']) && $_POST['artist'] !== 'all' ? sanitize_text_field($_POST['artist']) : '';
    $sort = isset($_POST['sort']) ? sanitize_text_field($_POST['sort']) : 'date_desc';
    $paged = isset($_POST['paged']) ? absint($_POST['paged']) : 1;

    // Build query args
    $args = array(
        'post_type' => 'record',
        'posts_per_page' => get_option('posts_per_page'),
        'paged' => $paged,
    );

    // Add taxonomy queries if set
    $tax_query = array('relation' => 'AND');

    if (!empty($genre)) {
        $tax_query[] = array(
            'taxonomy' => 'record_genre',
            'field' => 'slug',
            'terms' => $genre,
        );
    }

    if (!empty($artist)) {
        $tax_query[] = array(
            'taxonomy' => 'record_artist',
            'field' => 'slug',
            'terms' => $artist,
        );
    }

    if (count($tax_query) > 1) {
        $args['tax_query'] = $tax_query;
    }

    // Handle sorting
    switch ($sort) {
        case 'title_asc':
            $args['orderby'] = 'title';
            $args['order'] = 'ASC';
            break;
        case 'title_desc':
            $args['orderby'] = 'title';
            $args['order'] = 'DESC';
            break;
        case 'date_desc':
            $args['meta_key'] = '_record_release_date';
            $args['orderby'] = 'meta_value';
            $args['order'] = 'DESC';
            break;
        case 'date_asc':
            $args['meta_key'] = '_record_release_date';
            $args['orderby'] = 'meta_value';
            $args['order'] = 'ASC';
            break;
        case 'artist_asc':
            $args['orderby'] = 'tax_artist';
            $args['order'] = 'ASC';
            break;
        case 'artist_desc':
            $args['orderby'] = 'tax_artist';
            $args['order'] = 'DESC';
            break;
        default:
            $args['meta_key'] = '_record_release_date';
            $args['orderby'] = 'meta_value';
            $args['order'] = 'DESC';
            break;
    }

    // Add special handling for artist sorting
    if ($sort === 'artist_asc' || $sort === 'artist_desc') {
        add_filter('posts_clauses', 'rewindrecords_order_by_artist_filter', 10, 2);
    }

    // Run the query
    $query = new WP_Query($args);

    // Start output buffer to capture the HTML
    ob_start();

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            ?>
            <div class="recordItem">
                <a href="<?php the_permalink(); ?>" class="recordLink">
                    <div class="recordCover">
                        <?php if (has_post_thumbnail()) : ?>
                            <img class="lazy" data-src="<?php the_post_thumbnail_url('medium'); ?>" alt="<?php the_title(); ?>">
                        <?php else : ?>
                            <div class="defaultCover">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100">
                                    <circle cx="50" cy="50" r="45" fill="#1A1A1A" />
                                    <circle cx="50" cy="50" r="42" fill="#333333" />
                                    <circle cx="50" cy="50" r="18" fill="#F5D042" />
                                    <circle cx="50" cy="50" r="3" fill="#1A1A1A" />
                                </svg>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="recordInfo">
                        <h2 class="smallTitle"><?php the_title(); ?></h2>
                        <?php
                        $artist_terms = get_the_terms(get_the_ID(), 'record_artist');
                        if ($artist_terms && !is_wp_error($artist_terms)) {
                            $artist_name = $artist_terms[0]->name;
                            echo '<p class="recordArtist">' . esc_html($artist_name) . '</p>';
                        }

                        // Display price if enabled
                        $price = get_post_meta(get_the_ID(), '_record_price', true);
                        if ($price && function_exists('rewindrecords_show_prices') && rewindrecords_show_prices()) {
                            echo '<div class="smallTitle primary">€' . esc_html(number_format((float)$price, 2, ',', '.')) . '</div>';
                        }
                        ?>
                    </div>
                </a>
            </div>
            <?php
        }
    } else {
        ?>
        <div class="noRecords">
            <p><?php _e('No records found matching your criteria.', 'rewindrecords'); ?></p>
        </div>
        <?php
    }

    // Get the HTML from the buffer
    $html = ob_get_clean();

    // Get pagination
    ob_start();

    // Get current URL parameters
    $current_url = get_post_type_archive_link('record');
    $params = array();

    if (!empty($genre)) {
        $params['record_genre'] = $genre;
    }

    if (!empty($artist)) {
        $params['record_artist'] = $artist;
    }

    if (!empty($sort)) {
        $params['sort'] = $sort;
    }

    // Build base URL with parameters
    if (!empty($params)) {
        $current_url = add_query_arg($params, $current_url);
    }

    // Check if URL already has parameters
    $format = (strpos($current_url, '?') !== false) ? '&paged=%#%' : '?paged=%#%';

    echo paginate_links(array(
        'base' => $current_url . $format,
        'format' => '',
        'current' => $paged, // Use the requested page number
        'total' => $query->max_num_pages,
        'prev_text' => '<i class="icon-arrow-left"></i> ' . __('Previous', 'rewindrecords'),
        'next_text' => __('Next', 'rewindrecords') . ' <i class="icon-arrow-right"></i>',
        'add_args' => false, // Don't add query args, we've already included them in the base
    ));
    $pagination = ob_get_clean();

    // Reset post data
    wp_reset_postdata();

    // Remove the artist sorting filter if it was added
    if (isset($_POST['sort']) && ($_POST['sort'] === 'artist_asc' || $_POST['sort'] === 'artist_desc')) {
        remove_filter('posts_clauses', 'rewindrecords_order_by_artist_filter', 10);
    }

    // Return the response
    wp_send_json_success(array(
        'html' => $html,
        'pagination' => $pagination,
    ));
}
add_action('wp_ajax_filter_records', 'rewindrecords_filter_records');
add_action('wp_ajax_nopriv_filter_records', 'rewindrecords_filter_records');

/**
 * Filter to sort records by artist name
 */
function rewindrecords_order_by_artist_filter($clauses, $wp_query) {
    global $wpdb;

    // Only modify queries for the record post type
    if (isset($wp_query->query['post_type']) && $wp_query->query['post_type'] === 'record') {
        // Join with the term relationships, taxonomy, and term tables
        $clauses['join'] .= "
            LEFT JOIN {$wpdb->term_relationships} ON ({$wpdb->posts}.ID = {$wpdb->term_relationships}.object_id)
            LEFT JOIN {$wpdb->term_taxonomy} ON ({$wpdb->term_relationships}.term_taxonomy_id = {$wpdb->term_taxonomy}.term_taxonomy_id)
            LEFT JOIN {$wpdb->terms} ON ({$wpdb->term_taxonomy}.term_id = {$wpdb->terms}.term_id)
        ";

        // Filter by the record_artist taxonomy
        $clauses['where'] .= " AND {$wpdb->term_taxonomy}.taxonomy = 'record_artist'";

        // Order by the term name
        $order = $wp_query->get('order') === 'DESC' ? 'DESC' : 'ASC';
        $clauses['orderby'] = "{$wpdb->terms}.name {$order}, {$wpdb->posts}.post_title ASC";

        // Group by post ID to avoid duplicates
        $clauses['groupby'] = "{$wpdb->posts}.ID";
    }

    return $clauses;
}

/**
 * Localize script for records archive
 */
function rewindrecords_localize_scripts() {
    if (is_post_type_archive('record')) {
        wp_localize_script('Records_Archive', 'recordsArchiveData', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'archiveUrl' => get_post_type_archive_link('record'),
            'nonce' => wp_create_nonce('records_filter_nonce'),
            'errorMessage' => __('Error loading records. Please try again.', 'rewindrecords'),
        ));
    }
}
add_action('wp_enqueue_scripts', 'rewindrecords_localize_scripts', 20);

/**
 * Set default sorting for record archives to newest first and handle pagination
 */
function rewindrecords_set_record_archive_sorting($query) {
    // Only modify main queries for the record archive on the frontend
    if (!is_admin() && $query->is_main_query() && is_post_type_archive('record')) {
        // Check if no sort parameter is set in the URL
        if (!isset($_GET['sort'])) {
            // Set default sorting to newest first
            $query->set('meta_key', '_record_release_date');
            $query->set('orderby', 'meta_value');
            $query->set('order', 'DESC');
        }

        // Handle pagination
        if (isset($_GET['paged']) && is_numeric($_GET['paged'])) {
            $paged = absint($_GET['paged']);
            $query->set('paged', $paged);
        }

        // Handle taxonomy filters
        $tax_query = array('relation' => 'AND');
        $has_tax_query = false;

        // Handle genre filter
        if (isset($_GET['record_genre']) && $_GET['record_genre'] !== 'all') {
            $tax_query[] = array(
                'taxonomy' => 'record_genre',
                'field' => 'slug',
                'terms' => sanitize_text_field($_GET['record_genre']),
            );
            $has_tax_query = true;
        }

        // Handle artist filter
        if (isset($_GET['record_artist']) && $_GET['record_artist'] !== 'all') {
            $tax_query[] = array(
                'taxonomy' => 'record_artist',
                'field' => 'slug',
                'terms' => sanitize_text_field($_GET['record_artist']),
            );
            $has_tax_query = true;
        }

        // Set tax query if we have any taxonomy filters
        if ($has_tax_query) {
            $query->set('tax_query', $tax_query);
        }
    }
}
add_action('pre_get_posts', 'rewindrecords_set_record_archive_sorting');

?>


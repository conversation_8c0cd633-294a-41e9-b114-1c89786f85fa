// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

body {
  &:not(.touch) {
    .randomRecordsSlider {
      .recordsSlider {
        &:hover {
          .recordSlide {
            opacity: .4;
            &:hover {
              opacity: 1;
            }
          }
        }
      }
    }
  }
}
.randomRecordsSlider {
  .sliderWrapper {
    padding-left: @vw118 + @vw16;
  }
  .col {
    display: inline-block;
    vertical-align: middle;
    width: 50%;
    &.right {
      text-align: right;
    }
  }
  .recordsSlider {
    margin: 0 -@vw15;
    margin-top: @vw55;
    white-space: nowrap;
    .flickity-viewport {
      overflow: visible;
    }
    .recordSlide {
      white-space: normal;
      display: inline-block;
      vertical-align: top;
      cursor: pointer;
      width: 25%;
      padding: 0 @vw15;
      .transitionMore(opacity, .45s);
      * {
        cursor: pointer;
      }
      .recordLink {
        display: block;
        text-decoration: none;
        color: @almostWhite;
      }

      .recordCover {
        position: relative;
        width: 100%;
        padding-bottom: 100%; // Square aspect ratio
        margin-bottom: @vw15;
        overflow: hidden;
        background: @vinylColor;
        .transform(translate3d(0,0,0));
        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          .transitionMore(transform, .3s);
        }

        .defaultCover {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: block;
          background: @vinylColor;
          .transform(translate3d(0,0,0));
          svg {
            width: 80%;
            height: 80%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }

      &:hover {
        .recordCover {
          img {
            transform: scale(1.05);
          }
        }
      }

      .recordInfo {
        padding: @vw10;

        .recordTitle {
          font-size: @vw18;
          font-family: @headingFont;
          font-weight: 400;
          letter-spacing: 1px;
          margin: 0 0 @vw5;
          color: @almostWhite;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .recordArtist {
          font-size: @vw16;
          font-family: @bodyFont;
          color: @secondaryColor;
          margin-bottom: @vw5;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .recordYear, .recordFormat {
          font-size: @vw14;
          font-family: @bodyFont;
          color: @grey;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .recordPrice {
          font-size: @vw16;
          font-family: @headingFont;
          font-weight: 400;
          letter-spacing: 1px;
          color: @secondaryColor;
          margin-top: @vw5;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  // Flickity customization
  .flickity-button {
    background-color: transparent;
    color: @almostWhite;
    border: 1px solid @almostWhite;
    border-radius: 50%;
    width: @vw120;
    height: @vw120;
    font-size: @vw36;
    position: absolute;
    top: 50%;
    .transform(translateY(-50%));
    transition: background 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    -webkit-transition: background 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    &:hover {
      background: @almostWhite;
      color: @hardBlack;
      border-color: @almostWhite;
    }

    &:focus {
      outline: none;
    }

    &.previous {
      left: @vw10;
    }

    &.next {
      right: @vw10;
    }
  }

  .flickity-button-icon {
    fill: currentColor;
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .randomRecordsSlider {
    .sliderWrapper {
      padding-left: @vw118-1160 + @vw16-1160;
    }

    .recordsSlider {
      margin: 0 -@vw15-1160;
      margin-top: @vw55-1160;

      .recordSlide {
        width: 33.333%;
        padding: 0 @vw15-1160;

        .recordCover {
          margin-bottom: @vw15-1160;
          border-radius: @vw10-1160;
          box-shadow: 0 @vw5-1160 @vw15-1160 rgba(0, 0, 0, 0.3);
        }

        .recordInfo {
          padding: @vw10-1160;

          .recordTitle {
            font-size: @vw18-1160;
            margin: 0 0 @vw5-1160;
          }

          .recordArtist {
            font-size: @vw16-1160;
            margin-bottom: @vw5-1160;
          }

          .recordYear, .recordFormat {
            font-size: @vw14-1160;
          }

          .recordPrice {
            font-size: @vw16-1160;
            margin-top: @vw5-1160;
          }
        }
      }
    }

    .flickity-button {
      width: @vw80-1160;
      height: @vw80-1160;
      font-size: @vw24-1160;

      &.previous {
        left: @vw10-1160;
      }

      &.next {
        right: @vw10-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .randomRecordsSlider {
    .sliderWrapper {
      padding-left: 0;
    }

    .normalTitle {
      text-align: center;
    }

    .col {
      display: block;
      width: 100%;
      margin-bottom: @vw50-580;

      &.right {
        text-align: center;
      }
    }

    .recordsSlider {
      margin: 0 -@vw15-580;
      margin-top: @vw30-580;

      .recordSlide {
        width: 80%;
        padding: 0 @vw15-580;

        .recordCover {
          margin-bottom: @vw15-580;
          box-shadow: 0 @vw5-580 @vw15-580 rgba(0, 0, 0, 0.3);
        }

        .recordInfo {
          padding: @vw10-580;

          .recordTitle {
            font-size: @vw40-580;
            margin: 0 0 @vw5-580;
          }

          .recordArtist {
            font-size: @vw40-580;
            margin-bottom: @vw5-580;
          }

          .recordYear, .recordFormat {
            font-size: @vw14-580;
          }

          .recordPrice {
            font-size: @vw22-580;
            margin-top: @vw5-580;
          }
        }
      }
    }

    .flickity-button {
      width: @vw100-580;
      height: @vw100-580;
      font-size: @vw20-580;

      &.previous {
        left: @vw10-580;
      }

      &.next {
        right: @vw10-580;
      }
    }
  }
}

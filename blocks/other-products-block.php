<?php
/**
 * Other Products Block Template
 */

// Get block attributes
$number = get_field('number') ?: 6;
$columns = get_field('columns') ?: 3;
$title = get_field('title') ?: __('Other Products', 'rewindrecords');
$view_all_link = get_field('view_all_link');

// Query args
$args = array(
    'post_type' => 'record',
    'posts_per_page' => $number,
    'orderby' => 'date',
    'order' => 'DESC',
    'tax_query' => array(
        array(
            'taxonomy' => 'record_product_type',
            'field' => 'slug',
            'terms' => 'other',
        ),
    ),
);

// Run the query
$products_query = new WP_Query($args);
?>

<div class="otherProductsBlock">
    <div class="contentWrapper">
        <div class="blockHeader">
            <h2 class="normalTitle"><?php echo esc_html($title); ?></h2>

            <?php if ($view_all_link) : ?>
                <a href="<?php echo esc_url($view_all_link); ?>" class="viewAllLink"><?php _e('View All', 'rewindrecords'); ?></a>
            <?php endif; ?>
        </div>

        <?php if ($products_query->have_posts()) : ?>
            <div class="productsGrid columns-<?php echo esc_attr($columns); ?>">
                <?php while ($products_query->have_posts()) : $products_query->the_post(); ?>
                    <div class="productItem">
                        <a href="<?php the_permalink(); ?>" class="productLink">
                            <div class="productCover">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('record-cover'); ?>
                                <?php else : ?>
                                    <div class="defaultCover">
                                        <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                            <rect width="480" height="480" x="16" y="16" fill="#333" rx="24" />
                                            <path d="M256 128c-70.7 0-128 57.3-128 128s57.3 128 128 128 128-57.3 128-128-57.3-128-128-128zm0 224c-52.9 0-96-43.1-96-96s43.1-96 96-96 96 43.1 96 96-43.1 96-96 96z" fill="#F5D042" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="productInfo">
                                <h3 class="smallTitle"><?php the_title(); ?></h3>

                                <?php
                                $price = get_post_meta(get_the_ID(), '_record_price', true);
                                if ($price && rewindrecords_show_prices()):
                                ?>
                                    <div class="productPrice">€<?php echo number_format($price, 2, ',', '.'); ?></div>
                                <?php endif; ?>

                                <?php
                                $stock = get_post_meta(get_the_ID(), '_record_stock', true);
                                if ($stock !== ''):
                                ?>
                                    <div class="productStock">
                                        <?php if (intval($stock) > 0): ?>
                                            <span class="inStock"><?php _e('In Stock', 'rewindrecords'); ?></span>
                                        <?php else: ?>
                                            <span class="outOfStock"><?php _e('Out of Stock', 'rewindrecords'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else : ?>
            <div class="noProducts">
                <p><?php _e('No products found.', 'rewindrecords'); ?></p>
            </div>
        <?php endif; ?>
        <?php wp_reset_postdata(); ?>
    </div>
</div>

<style>
    .otherProductsBlock {
        padding: 40px 0;
    }

    .otherProductsBlock .blockHeader {
        display: block;
        margin-bottom: 30px;
        overflow: hidden;
    }

    .otherProductsBlock .blockHeader .normalTitle {
        float: left;
    }

    .otherProductsBlock .viewAllLink {
        float: right;
        display: inline-block;
        padding: 8px 16px;
        background-color: #f5f5f5;
        border-radius: 4px;
        text-decoration: none;
        color: #333;
        transition: background-color 0.3s;
    }

    .otherProductsBlock .viewAllLink:hover {
        background-color: #e0e0e0;
    }

    .otherProductsBlock .productsGrid {
        display: block;
        margin: 0 -15px;
        overflow: hidden;
    }

    .otherProductsBlock .productItem {
        display: inline-block;
        vertical-align: top;
        padding: 0 15px;
        margin-bottom: 30px;
    }

    .otherProductsBlock .productsGrid.columns-2 .productItem {
        width: 50%;
    }

    .otherProductsBlock .productsGrid.columns-3 .productItem {
        width: 33.333%;
    }

    .otherProductsBlock .productsGrid.columns-4 .productItem {
        width: 25%;
    }

    .otherProductsBlock .productItem {
        transition: transform 0.3s;
    }

    .otherProductsBlock .productItem:hover {
        transform: translateY(-5px);
    }

    .otherProductsBlock .productLink {
        display: block;
        text-decoration: none;
        color: inherit;
    }

    .otherProductsBlock .productCover {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .otherProductsBlock .productCover img {
        display: block;
        width: 100%;
        height: auto;
        transition: transform 0.3s;
    }

    .otherProductsBlock .productItem:hover .productCover img {
        transform: scale(1.05);
    }

    .otherProductsBlock .defaultCover {
        padding-top: 100%;
        position: relative;
        background-color: #f5f5f5;
    }

    .otherProductsBlock .defaultCover svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .otherProductsBlock .productInfo {
        padding: 10px 0;
    }

    .otherProductsBlock .smallTitle {
        margin: 0 0 5px;
        font-size: 16px;
        font-weight: 600;
    }

    .otherProductsBlock .productPrice {
        font-size: 18px;
        font-weight: 700;
        color: #333;
        margin-bottom: 5px;
    }

    .otherProductsBlock .productStock {
        font-size: 14px;
        margin-top: 5px;
    }

    .otherProductsBlock .inStock {
        color: #2ecc71;
    }

    .otherProductsBlock .outOfStock {
        color: #e74c3c;
    }

    .otherProductsBlock .noProducts {
        padding: 30px;
        text-align: center;
        background-color: #f5f5f5;
        border-radius: 8px;
    }

    @media (max-width: 768px) {
        .otherProductsBlock .productsGrid.columns-3 .productItem,
        .otherProductsBlock .productsGrid.columns-4 .productItem {
            width: 50%;
        }

        .otherProductsBlock .blockHeader {
            text-align: center;
        }

        .otherProductsBlock .blockHeader .normalTitle,
        .otherProductsBlock .blockHeader .viewAllLink {
            float: none;
            display: block;
            margin: 0 auto 15px;
        }
    }

    @media (max-width: 480px) {
        .otherProductsBlock .productsGrid.columns-2 .productItem,
        .otherProductsBlock .productsGrid.columns-3 .productItem,
        .otherProductsBlock .productsGrid.columns-4 .productItem {
            width: 100%;
        }
    }
</style>

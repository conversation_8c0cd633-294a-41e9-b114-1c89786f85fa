// out: false
@import 'vw_values.less';
@import 'constants.less';

* {
  box-sizing: border-box;
  cursor:default;
  letter-spacing: 0;
  margin:0;
  padding:0;
  position:relative;
  &::selection {
    background: @primaryColor;
    color: @almostWhite;
  }
  &::-webkit-selection {
    background: @primaryColor;
    color: @almostWhite;
  }
  &:focus {
    outline: none;
  }
}

html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

html {
  overflow-x: hidden;
}
body {
  background: @hardBlack;
  color: @almostWhite;
  font-family: @bodyFont;
  font-weight: 200;
  font-style: normal;
  overflow: hidden;
  line-height: 1.5;
  font-size: @vw22;
  strong {
    font-weight: 700;
  }
  a {
      cursor: pointer;
      color: @almostWhite;
      text-decoration: none;
  }
  p {
    a {
      cursor: pointer;
      color: @almostWhite;
      text-decoration: none;
      font-weight: 700;
      .transitionMore(all, .15s);
      &:hover {
        opacity: .6;
      }
    }
  }
  ul {
    line-height: 1.4;
  }
}

img {
  pointer-events: none;
}

body.touch {
  a,
  .button {
    &:hover {
      opacity: 1 !important;
      color: inherit !important;
      background: inherit !important;
      transform: none !important;
      transition: none !important;
    }
  }
  .button {
    &:hover {
      background: @primaryColor !important;
    }
  }
}

[data-scroll-section] {
  background: @almostWhite;
  // padding: @vw100 + @vw30 + @vw5 0 0 0;
}

[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}

section {
  margin: @vw100 + @vw50 0;
  &:first-of-type {
    margin-top: 0;
    padding-top: @vw100 * 2;
  }
  &.white {
    padding: @vw100 0;
    border-radius: @vw30;
    color: @hardBlack;
    background: @almostWhite;
  }
  .staggerWordsChild(@i, @transition, @delay: 0s) when (@i > 0) {
    &:nth-child(@{i}) {
        .word {
          transition-delay: (@i * @transition + @delay);
        }
    }
    .staggerWordsChild(@i - 1, @transition, @delay);
}
  &.inview {
    .textLink {
      transition: opacity .3s .45s ease-in-out, transform .3s .45s ease-in-out;
      -webkit-transition: opacity .3s .45s ease-in-out, transform .3s .45s ease-in-out;
      opacity: 1;
      .transform(translateY(0));
      .stagger(10, .15s);
    }
    [data-lines] {
      visibility: visible;
      .line {
        .staggerWordsChild(100, 0.15s, 0.4s);
        .word {
          .transform(translateY(0));
          .transitionMore(transform, .6s, 0s, cubic-bezier(0, 0.55, 0.45, 1));
        }
      }
    }
    .links {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity .6s .45s ease-in-out, transform .6s .45s ease-in-out;
      -webkit-transition: opacity .6s .45s ease-in-out, transform .6s .45s ease-in-out;
    }
  }
  [data-lines] {
    .line {
      position: relative;
      overflow: hidden;
      .word {
        .transform(translateY(100%));
        will-change: transform;
      }
    }
  }
}

.links {
  opacity: 0;
  .transform(translateY(@vw30));
}

[data-lines] {
  visibility: hidden;
}

.contentWrapper {
  display: block;
  width: 100%;
  padding: 0 @vw68;
  &.smaller {
    padding: 0 @vw104 + @vw112 + @vw16;
  }
  &.small {
    padding: 0 @vw68 + @vw118 + @vw16 + @vw118 + @vw16;
  }
}

.bodyBackground{
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: .5;
  top: 0;
  left: 0;
  pointer-events: none;
  background-size: cover;
  background-position: top;
}

.noise {
  background-color: rgba(20,20,20,.3);
  background-position: 0;
  height: 100%;
  opacity: .09;
  mix-blend-mode: difference;
  pointer-events: none;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
}

@keyframes noise{
  0%{
      background-position: 0 0;
  }
  100%{
      background-position: 100% 100%;
  }
}

.textLink {
  display: inline-block;
  cursor: pointer;
  font-size: @vw32;
  line-height: 1;
  font-family: @headingFont;
  font-weight: 400;
  font-style: normal;
  padding-bottom: @vw5;
  letter-spacing: 1px;
  color: @almostWhite;
  text-transform: uppercase;
  text-decoration: none;
  opacity: 0;
  .transform(translateY(@vw20));
  cursor: pointer;
  &:not(:last-child) {
    margin-right: @vw22;
  }
  &:hover {
    .innerWrapper {
      .transform(translateY(-@vw32));
      .innerText {
        &.absolute {
          &:first-child {
            .transform(rotate(20deg));
          }
          &:last-child {
            .transform(rotate(0deg));
          }
        }
      }
    }
    .divider {
      svg {
        line {
          stroke-dasharray: 4;
          stroke-dashoffset: 10;
        }
      }
    }
  }
  * {
    cursor: pointer;
  }
  i {
    display: inline-block;
    vertical-align: middle;
    margin-right: @vw20;
    margin-bottom: @vw10;
    font-size: @vw22;
  }
  .innerMask {
    display: inline-block;
    height: @vw32;
    overflow: hidden;
    position: relative;
  }
  .innerWrapper {
    display: inline-block;
    width: auto;
    vertical-align: middle;
    .transitionMore(transform, .3s, 0s, ease-out);
    .innerText {
      display: block;
      z-index: 1;
      &.hidden {
        opacity: 0;
        display: block;
      }
      &.absolute {
        // position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        .transitionMore(transform, .3s, 0s, ease-out);
        &:first-child {
          transform-origin: right center;
        }
        &:last-child {
          .transform(rotate(20deg));
          transform-origin: left center;
        }
      }
    }
  }
  .divider {
    position: absolute;
    bottom: auto;
    width: 100%;
    left: 0;
    bottom: -2px;
    display: inline-block;
    vertical-align: middle;
    svg {
      height: auto;
      width: 100%;
      object-fit: contain;
      line {
        stroke: @almostWhite;
        stroke-dasharray: 4;
        stroke-dashoffset: 0;
        transition: stroke-dashoffset .3s 0s ease-out, stroke-dasharray .3s 0s ease-out;
      }
    }
  }
}

.button {
  border: 1px solid @primaryColor;
  padding: @vw12 @vw20;
  background: @primaryColor;
  display: inline-table;
  cursor: pointer;
  font-family: @headingFont;
  font-weight: 400;
  font-style: normal;
  letter-spacing: 1px;
  text-align: left;
  color: @almostWhite;
  text-decoration: none;
  transition: color .3s, background .3s, border .3s;
  * {
    cursor: pointer;
  }
  &:hover {
    color: @primaryColor;
    background: transparent;
    .arrows {
      border-color: @primaryColor;
      i {
        &:first-child {
          .transform(translate(-50%, -50%));
        }
        &:last-child {
          .transform(translate(50%, -150%) scale(.5));
        }
      }
    }
  }
  .innerText {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - @vw34);
    padding-right: @vw16;
  }
  .arrows {
    display: inline-block;
    vertical-align: middle;
    width: @vw34;
    height: @vw34;
    font-size: @vw24;
    border: 1px solid @almostWhite;
    line-height: @vw34;
    text-align: center;
    position: relative;
    overflow: hidden;
    .transitionMore(border-color,.3s);
    i {
      position: absolute;
      left: 50%;
      top: 50%;
      .transform(translate(-50%, -50%));
      .transitionMore(transform, .6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
      &:first-child {
        .transform(translate(-150%, 50%) scale(.5));
      }
    }
  }
}

.arrowButton {
  cursor: pointer;
  display: inline-block;
  border: 1px solid @almostWhite;
  font-size: @vw32;
  text-align: center;
  line-height: @vw63;
  width: @vw63;
  height: @vw63;
  overflow: hidden;
  position: relative;
  vertical-align: middle;
  transition: color .3s, border-color .3s;
  &.prev {
    &:hover {
      i {
        &:last-child {
          .transform(translate(-200%, -50%) scale(.5));
        }
      }
    }
    i {
      &:first-child {
        .transform(translate(100%, -50%) scale(.5));
      }
    }
  }
  &:hover {
    border-color: @primaryColor;
    color: @primaryColor;
    i {
      &:first-child {
        .transform(translate(-50%, -50%));
      }
      &:last-child {
        .transform(translate(100%, -50%) scale(.5));
      }
    }
  }
  i {
    cursor: pointer;
    position: absolute;
    left: 50%;
    top: 50%;
    .transform(translate(-50%, -50%));
    .transitionMore(transform, .6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
    &:first-child {
      .transform(translate(-200%, -50%) scale(.5));
    }
  }
}

#background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.dg.ac {
  z-index: 3 !important;
}

@media all and (max-width: 1160px) {
  body {
    font-size: @vw18-1160;
  }
  section {
    margin: @vw100-1160 + @vw20-1160 0;
    &:first-of-type {
      padding-top: @vw100-1160 * 2;
    }
    &.white {
      padding: @vw100-1160 0;
      border-radius: @vw30-1160;
    }
  }
  .links {
    .transform(translateY(@vw30-1160));
  }
  .contentWrapper {
    padding: 0 @vw40-1160;
    &.smaller {
      padding: 0 @vw40-1160 + @vw32-1160;
    }
    &.small {
      padding: 0 @vw40-1160 + @vw32-1160;
    }
  }
  .button {
    padding: @vw12-1160 @vw20-1160;
    .innerText {
      width: calc(100% - @vw34-1160);
      padding-right: @vw16-1160;
    }
    .arrows {
      width: @vw34-1160;
      height: @vw34-1160;
      font-size: @vw24-1160;
      line-height: @vw34-1160;
    }
  }
  .textLink {
    font-size: @vw22-1160;
    &:hover {
      .innerWrapper {
        .transform(translateY(-50%));
      }
    }
    i {
      font-size: @vw20-1160;
    }
    .innerMask {
      height: @vw22-1160;
    }
    .innerText {
      width: calc(100% - @vw34-1160);
      padding-right: @vw16-1160;
    }
    &.bigger {
      font-size: @vw50-1160;
      .innerText {
        padding-right: @vw30-1160;
        width: calc(100% - @vw64-1160);
      }
      .arrows {
        width: @vw50-1160;
        height: @vw50-1160;
        font-size: @vw30-1160;
        line-height: @vw50-1160;
      }
    }
    .arrows {
      width: @vw34-1160;
      height: @vw34-1160;
      line-height: @vw34-1160;
    }
  }
  .arrowButton {
    font-size: @vw32-1160;
    line-height: @vw63-1160;
    width: @vw63-1160;
    height: @vw63-1160;
  }
} 

@media all and (max-width: 580px) {
  body {
    font-size: @vw22-580;
  }
  section {
    margin: @vw100-580 + @vw20-580 0;
    &:first-of-type {
      padding-top: @vw100-580 * 2.5;
    }
    &.white {
      padding: @vw100-580 0;
      border-radius: @vw30-580;
    }
  }
  .links {
    .transform(translateY(@vw30-580));
  }
  .contentWrapper {
    padding: 0 @vw22-580;
    &.smaller {
      padding: 0 @vw22-580;
    }
    &.small {
      padding: 0 @vw22-580;
    }
  }
  .button {
    padding: @vw12-580 @vw20-580;
    .innerText {
      width: calc(100% - @vw34-580);
      padding-right: @vw16-580;
    }
    .arrows {
      width: @vw34-580;
      height: @vw34-580;
      font-size: @vw24-580;
      line-height: @vw34-580;
    }
  }
  .textLink {
    font-size: @vw30-580;
    &.bigger {
      font-size: @vw30-580;
      .innerText {
        width: calc(100% - @vw34-580);
        padding-right: @vw16-580;
      }
      .arrows {
        width: @vw34-580;
        height: @vw34-580;
        line-height: @vw34-580;
      }
    }
    i {
      font-size: @vw20-580;
    }
    .innerMask {
      height: @vw30-580;
    }
    .innerText {
      width: calc(100% - @vw34-580);
      padding-right: @vw16-580;
    }
    .arrows {
      width: @vw34-580;
      height: @vw34-580;
      line-height: @vw34-580;
    }
  }
  .arrowButton {
    font-size: @vw32-580;
    line-height: @vw63-580;
    width: @vw63-580;
    height: @vw63-580;
  }
}

// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.mediaTextBlock {
  .col {
    display: inline-block;
    vertical-align: middle;
    width: 50%;
    &.textCol {
      padding-left: @vw118 + @vw16;
    }
  }
  .mediaWrapper {
    height: auto;
    position: relative;
    overflow: hidden;
    .innerImage {
      width: 100%;
      overflow: hidden;
      padding-bottom: 100%;
      height: 0;
      img,
      video {
        position: absolute;
        top: -10%;
        height: 120%;
        width: 100%;
        object-fit: cover;
      }
    }
  }

  .normalTitle {
    margin-bottom: @vw20;
  }

  .col{
    .media-text-title {
      font-family: @headingFont;
      font-size: @vw32;
      font-weight: 400;
      letter-spacing: 2px;
      color: @almostWhite;
      margin-bottom: @vw20;
      line-height: 1.1;
    }

    .media-text-content-text {
      font-family: @bodyFont;
      font-size: @vw16;
      line-height: 1.6;
      color: @grey;
      margin-bottom: @vw30;

      p {
        margin-bottom: @vw15;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .links {
    margin-top: @vw60;
  }

  // Media position variations
  &.media-left {
    .cols {
      direction: ltr;
    }
  }

  &.media-right {
    .cols {
      direction: rtl;

      .col {
        direction: ltr;
      }
    }
  }
}

// Responsive styles
@media all and (max-width: 1160px) {
  .mediaTextBlock {
    padding: @vw50-1160 0;

    .col {
      &.textCol {
        padding-left: @vw80-1160;
      }
    }

    .mediaWrapper {
      .innerImage {
        img,
        video {
          top: -10%;
          height: 120%;
        }
      }
    }

    .normalTitle {
      margin-bottom: @vw20-1160;
    }

    .col {
      .media-text-title {
        font-size: @vw32-1160;
        letter-spacing: 1.5px;
        margin-bottom: @vw20-1160;
      }

      .media-text-content-text {
        font-size: @vw16-1160;
        margin-bottom: @vw30-1160;

        p {
          margin-bottom: @vw15-1160;
        }
      }
    }

    .links {
      margin-top: @vw60-1160;
    }
  }
}

@media all and (max-width: 580px) {
  .mediaTextBlock {
    padding: @vw50-580 0;

    .col {
      display: block;
      width: 100%;
      margin-bottom: @vw30-580;

      &:last-child {
        margin-bottom: 0;
      }

      &.textCol {
        padding-left: 0;
      }

      .media-text-title {
        font-size: @vw32-580;
        margin-bottom: @vw20-580;
      }

      .media-text-content-text {
        font-size: @vw16-580;
        margin-bottom: @vw30-580;

        p {
          margin-bottom: @vw15-580;
        }
      }
    }

    .mediaWrapper {
      .innerImage {
        border-radius: @vw10-580;
      }
    }

    .links {
      margin-top: @vw40-580;

      a {
        font-size: @vw18-580;

        i {
          font-size: @vw16-580;
        }

        &:hover {
          i {
            transform: translateX(@vw5-580);
          }
        }
      }
    }

    &.media-left,
    &.media-right {
      .cols {
        direction: ltr;
      }
    }
  }
}

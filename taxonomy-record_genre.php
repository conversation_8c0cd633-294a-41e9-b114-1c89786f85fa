<?php
/**
 * The template for displaying record genre archives
 */

get_header();

$current_term = get_queried_object();
?>

<section class="recordsArchive genreArchive">
    <div class="contentWrapper">
        <h1 class="bigTitle white"><?php echo esc_html($current_term->name); ?></h1>

        <?php if (!empty($current_term->description)) : ?>
            <div class="termDescription">
                <?php echo wpautop($current_term->description); ?>
            </div>
        <?php endif; ?>

        <div class="recordsGrid">
            <?php if (have_posts()) : ?>
                <?php while (have_posts()) : the_post(); ?>
                    <div class="recordItem">
                        <a href="<?php the_permalink(); ?>" class="recordLink">
                            <div class="recordCover">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('medium'); ?>
                                <?php else : ?>
                                    <div class="defaultCover">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100">
                                            <circle cx="50" cy="50" r="45" fill="#1A1A1A" />
                                            <circle cx="50" cy="50" r="42" fill="#333333" />
                                            <circle cx="50" cy="50" r="18" fill="#F5D042" />
                                            <circle cx="50" cy="50" r="3" fill="#1A1A1A" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="recordInfo">
                                <h2 class="recordTitle"><?php the_title(); ?></h2>

                                <?php
                                $artists = get_the_terms(get_the_ID(), 'record_artist');
                                if ($artists && !is_wp_error($artists)) :
                                ?>
                                    <div class="recordArtist">
                                        <?php echo esc_html($artists[0]->name); ?>
                                    </div>
                                <?php endif; ?>

                                <?php
                                $year = get_post_meta(get_the_ID(), '_record_year', true);
                                if ($year) :
                                ?>
                                    <div class="recordYear"><?php echo esc_html($year); ?></div>
                                <?php endif; ?>

                                <?php
                                $formats = get_the_terms(get_the_ID(), 'record_format');
                                if ($formats && !is_wp_error($formats)) :
                                    $format_names = array();
                                    foreach ($formats as $format) {
                                        $format_names[] = $format->name;
                                    }
                                ?>
                                    <div class="recordFormat"><?php echo esc_html(implode(', ', $format_names)); ?></div>
                                <?php endif; ?>

                                <?php
                                $suggested_price = get_post_meta(get_the_ID(), '_record_suggested_price', true);
                                if ($suggested_price && rewindrecords_show_prices()) :
                                ?>
                                    <div class="recordPrice">€<?php echo number_format($suggested_price, 2, ',', '.'); ?></div>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                <?php endwhile; ?>

                <div class="pagination">
                    <?php
                    echo paginate_links(array(
                        'prev_text' => '<i class="icon-arrow-left"></i> ' . __('Previous', 'rewindrecords'),
                        'next_text' => __('Next', 'rewindrecords') . ' <i class="icon-arrow-right"></i>',
                    ));
                    ?>
                </div>
            <?php else : ?>
                <div class="noRecords">
                    <p><?php _e('No records found in this genre.', 'rewindrecords'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<?php
get_footer();

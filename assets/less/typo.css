/* Colors */
/* Dark record color */
/* Deeper black */
/* Vinyl red */
/* Light background */
/* Dark vinyl color */
/* Record label yellow */
/* Fonts */
.hugeTitle.white,
.bigTitle.white,
.biggerTitle.white {
  color: #F5F5F5;
}
.hugeTitle {
  font-size: 8.102vw;
  text-decoration: none;
  font-family: 'Bebas Neue', cursive;
  font-weight: 400;
  /* Bebas Neue only has one weight */
  font-style: normal;
  letter-spacing: 2px;
  line-height: 0.9;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.hugeTitle.link {
  cursor: pointer;
  color: #CFBA9C;
}
.hugeTitle.link:hover {
  opacity: 0.6;
}
.hugeTitle.link span {
  cursor: pointer;
}
.bigTitle {
  font-size: 8.102vw;
  line-height: 0.9;
  letter-spacing: 2px;
  font-family: '<PERSON><PERSON>eue', cursive;
  font-weight: 400;
  font-style: normal;
}
.bigTitle.compact {
  font-size: 7.523vw;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Bebas Neue', cursive;
  font-weight: 400;
  font-style: normal;
}
.bigTitle .line {
  position: relative;
  overflow: hidden;
}
.mediumTitle {
  font-size: 2.894vw;
  letter-spacing: 2px;
  font-family: 'Bebas Neue', cursive;
  font-weight: 400;
  font-style: normal;
  line-height: 1.1;
  text-transform: uppercase;
}
.subTitle {
  font-size: 1.389vw;
  line-height: 1.4;
  font-family: 'Bebas Neue', cursive;
  font-weight: 400;
  font-style: normal;
  letter-spacing: 1px;
}
.subTitle.primary {
  color: #CFBA9C;
}
.subTitle.secondary {
  color: #E83A14;
}
.tinyTitle {
  font-size: 0.926vw;
  line-height: 1.4;
  text-transform: uppercase;
  font-family: 'Bebas Neue', cursive;
  font-weight: 400;
  letter-spacing: 1px;
}
.normalTitle {
  font-size: 2.894vw;
  line-height: 1.1;
  text-transform: uppercase;
  font-family: 'Bebas Neue', cursive;
  font-weight: 400;
  letter-spacing: 1px;
}
.normalTitle.primary {
  color: #CFBA9C;
}
.normalTitle .primary {
  color: #CFBA9C;
}
.normalTitle .line {
  position: relative;
  overflow: hidden;
}
.link,
.smallTitle {
  font-size: 1.852vw;
  line-height: 1.4;
  text-transform: uppercase;
  font-family: 'Bebas Neue', cursive;
  font-weight: 400;
  letter-spacing: 1px;
}
.link.primary,
.smallTitle.primary {
  color: #CFBA9C;
}
.smallTitle {
  line-height: 1.1;
}
.text {
  font-family: 'Lora', serif;
  line-height: 1.8;
}
.text.bigger {
  font-size: 1.273vw;
  text-transform: uppercase;
}
.text.bigger p {
  font-size: 1.273vw;
  text-transform: uppercase;
}
.text.white p {
  color: #B3B3B3;
}
.text:not(:first-child) {
  margin-top: 1.157vw;
}
.text p {
  font-family: 'Lora', serif;
  line-height: 1.8;
  font-weight: 400;
}
.text p:not(:last-child) {
  margin-bottom: 1.273vw;
}
@media all and (max-width: 1160px) {
  .hugeTitle {
    font-size: 6.207vw;
  }
  .bigTitle {
    font-size: 7.069vw;
  }
  .bigTitle.compact {
    font-size: 6.034vw;
  }
  .mediumTitle {
    font-size: 4.31vw;
  }
  .normalTitle {
    font-size: 3.448vw;
  }
  .subTitle {
    font-size: 2.069vw;
  }
  .tinyTitle {
    font-size: 1.379vw;
  }
  .text.bigger {
    font-size: 1.897vw;
  }
  .text.bigger p {
    font-size: 1.897vw;
  }
  .text:not(:first-child) {
    margin-top: 1.724vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 1.897vw;
  }
}
@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: 17.241vw;
  }
  .bigTitle {
    font-size: 12.069vw;
  }
  .bigTitle.compact {
    font-size: 12.069vw;
  }
  .mediumTitle {
    font-size: 8.62vw;
  }
  .normalTitle {
    font-size: 6.897vw;
  }
  .subTitle {
    font-size: 4.137vw;
  }
  .tinyTitle {
    font-size: 2.758vw;
  }
  .text.bigger {
    font-size: 3.793vw;
  }
  .text.bigger p {
    font-size: 3.793vw;
  }
  .text:not(:first-child) {
    margin-top: 3.448vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 3.793vw;
  }
}
